 <div class="ads_methods">
    <div class="ads_method_links">
        <div class="ads_link active">
            <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span>
            <span class="ads_type" data-metLink="vod">Video on Demand</span>
            <span class="ads_check"><i class="fa-solid fa-square-check"></i></span>
        </div>
        <div class="ads_link">
            <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span>
            <span class="ads_type" data-metLink="liv">Livestream</span>
            <span class="ads_check"><i class="fa-solid fa-square-check"></i></span>
        </div>
        <div class="ads_link">
            <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span>
            <span class="ads_type" data-metLink="shc">Share content</span>
            <span class="ads_check"><i class="fa-solid fa-square-check"></i></span>
        </div>
    </div>
    <?php 
        $media = null;
        $followers = 0;
        if($follower = $socialFollowerCount->first())
        {
            $media = $follower->media;
            $followers = $follower->followers;
            if($media == 'facebook')
            {
                $src = URL:: asset('/').'/assets/front-end/images/icons/col_icon_facebook.png';
            }elseif($media == 'instagram')
            {
                $src = URL:: asset('/').'/assets/front-end/images/icons/col_icon_instagram.png';
            }elseif($media == 'twitter')
            {
                $src = URL:: asset('/').'/assets/front-end/images/icons/col_icon_twitter.png';
            }elseif($media == 'youtube')
            {
                $src = URL:: asset('/').'/assets/front-end/images/icons/col_icon_youtube.png';
            }elseif($media == 'twitch')
            {
                $src = URL:: asset('/').'/assets/front-end/images/icons/col_icon_twitch.png';
            }elseif($media == 'tiktok')
            {
                $src = URL:: asset('/').'/assets/front-end/images/icons/col_icon_tiktok.png';
            }
            // elseif($media == 'snapchat')
            // {
            //     $src = URL:: asset('/').'/assets/front-end/images/icons/col_icon_snapchat.png';
            // }
        }else{
                $src = URL:: asset('/').'/assets/front-end/images/icons/col_icon_facebook.png';
        }
        $pricelive = 0;
        $addpricelive = 0;
        $livestream_minutes = 5;
        $price = 0;
        $addprice = 0;
        $minutes = 5;
        $priceshare = 0;
        if(Auth::user()->advertisingMethodDetail != null)
        {
            $fieldName = 'video_price_'.$media;
            $addfieldName = 'add_video_price_'.$media;
            
            $price = Auth::user()->advertisingMethodDetail->$fieldName;
            $addprice = Auth::user()->advertisingMethodDetail->$addfieldName;
    
            $minutes = (int)Auth::user()->advertisingMethodDetail->video_minutes;
           
            $fieldNameLive = 'livestream_price_'.$media;
            $addfieldNameLive = 'add_livestream_price_'.$media;
            
            $pricelive = Auth::user()->advertisingMethodDetail->$fieldNameLive;
            $addpricelive = Auth::user()->advertisingMethodDetail->$addfieldNameLive;

            $livestream_minutes = (int)Auth::user()->advertisingMethodDetail->livestream_minutes;

            $fieldNameShare = 'share_price_'.$media;
            $priceshare = Auth::user()->advertisingMethodDetail->$fieldNameShare;
        }
    ?>
    <div class="ads_method_content">
        <div class="ads_content vod-cont current" data-metCont="vod">
            <div class="row">
                <div class="col-12">
                    <div class="checkone d-flex">
                        <div class="iconbox"> 
                            <div class="socialCnt nmt @if(Auth::user()->status != 1) uncheck @endif" > 
                                <img src="{{$src}}" class="iconBlack" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="form-group pricing_top">
                                <label class="priceSpan">Fixed price up to <span id="videoMinute">{{$minutes}} </span> Minutes</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">€</span>
                                    <input type="text" name="video_price_{{$media}}" placeholder="0" class="hidden-textbox form-control text-start ds" 
                                    id="videoPrice" data-media="{{$media}}" data-followers="{{$followers}}" pattern="\d+\.?\d*" value="{{ $price }}" >
                                </div>
                                <small>Depending on your followers, we suggest a price of you</small>
                                <span class="suggest_price">{{ number_format($videoDemandPrice ?? 0, 2) }} €</span>
                            </div>
                            <input type="hidden" name="video_minutes" id="totalVideoMinutes" value="{{$minutes}}">
                        </div>
                        <div class="inputs">
                            <div class="form-group pricing_top">
                                <label class="priceSpan datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info "></i> Additional variable price per <span id="addVideoMinute"> {{$minutes}} </span> Minutes</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">€</span>
                                    <input type="text" name="add_video_price_{{$media}}" placeholder="0" class="hidden-textbox form-control text-start ds" 
                                    id="addVideoPrice" data-media="{{$media}}" data-followers="{{$followers}}" pattern="\d+\.?\d*" value="{{$addprice}}" >
                                </div>
                                <small>Depending on your followers, we suggest a price of you</small>
                                <span class="suggest_price">{{number_format($addVideoDemandPrice,2) }} €</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="market_place_slider">
                        <div class="placeSlidee_title"> Your price on marketplace</div>
                        <div class="place_slider">
                            <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css">
                            <div class="main">
                                <button type="button" class="btn-range" onclick="videoRange('minus')" data-dir="minus"><i class="fa-solid fa-angle-left"></i></button>
                                <div id="storlekslider" ></div>
                                <!-- <input type="text" name="storlek" id="storlek_testet" /> -->
                                <button type="button" class="btn-range" onclick="videoRange('plus')" data-dir="plus"><i class="fa-solid fa-angle-right"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
               
                @foreach($socialFollowerCount as $key => $social)
                    @if($media != $social->media)
                    <?php 
                            $newPrice = 0;
                            $addNewPrice = 0;
                    
                            if($social->media == 'facebook')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newPrice = Auth::user()->advertisingMethodDetail->video_price_facebook;
                                    $addNewPrice = Auth::user()->advertisingMethodDetail->add_video_price_facebook;
                                }
                                $srcvideo = URL:: asset('/').'/assets/front-end/images/icons/col_icon_facebook.png';
                            }elseif($social->media == 'instagram')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newPrice = Auth::user()->advertisingMethodDetail->video_price_instagram;
                                    $addNewPrice = Auth::user()->advertisingMethodDetail->add_video_price_instagram;
                                }
                                $srcvideo = URL:: asset('/').'/assets/front-end/images/icons/col_icon_instagram.png';
                            }elseif($social->media == 'twitter')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newPrice = Auth::user()->advertisingMethodDetail->video_price_twitter;
                                    $addNewPrice = Auth::user()->advertisingMethodDetail->add_video_price_twitter;
                                }
                                $srcvideo = URL:: asset('/').'/assets/front-end/images/icons/col_icon_twitter.png';
                            }elseif($social->media == 'youtube')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newPrice = Auth::user()->advertisingMethodDetail->video_price_youtube;
                                    $addNewPrice = Auth::user()->advertisingMethodDetail->add_video_price_youtube;
                                }
                                $srcvideo = URL:: asset('/').'/assets/front-end/images/icons/col_icon_youtube.png';
                            }elseif($social->media == 'twitch')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newPrice = Auth::user()->advertisingMethodDetail->video_price_twitch;
                                    $addNewPrice = Auth::user()->advertisingMethodDetail->add_video_price_twitch;
                                }
                                $srcvideo = URL:: asset('/').'/assets/front-end/images/icons/col_icon_twitch.png';
                            }elseif($social->media == 'tiktok')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newPrice = Auth::user()->advertisingMethodDetail->video_price_tiktok;
                                    $addNewPrice = Auth::user()->advertisingMethodDetail->add_video_price_tiktok;
                                }
                                $srcvideo = URL:: asset('/').'/assets/front-end/images/icons/col_icon_tiktok.png';
                            }
                            // elseif($social->media == 'snapchat')
                            // {
                            //     if(Auth::user()->advertisingMethodDetail != null)
                            //     {
                            //         $newPrice = Auth::user()->advertisingMethodDetail->video_price_snapchat;
                            //         $addNewPrice = Auth::user()->advertisingMethodDetail->add_video_price_snapchat;
                            //     }
                            //     $srcvideo = URL:: asset('/').'/assets/front-end/images/icons/col_icon_snapchat.png';
                            // }
                        ?>
                        <div class="col-12">
                            <div class="checkoneOn d-flex">
                                <div class="iconbox">
                                    <div class="socialCnt nmt  @if(Auth::user()->status != 1) uncheck @endif" >
                                        <img src="{{$srcvideo}}" class="iconColored" alt="">
                                    </div>
                                </div>
                                <div class="inputs">
                                    <div class="pricenh" id="media{{$social->media}}">
                                        {{number_format($newPrice,2)}} €
                                    </div>
                                    <input type="hidden" id="videoPrice{{$social->media}}" name="video_price_{{$social->media}}" 
                                    value="{{$newPrice}}">
                                    <input type="hidden" id="addVideoPrice{{$social->media}}" name="add_video_price_{{$social->media}}" 
                                    value="{{$addNewPrice}}">
                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach

                @foreach($remainingMedias as $rmedia)
                <?php 
                     if($rmedia == 'facebook')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_facebook-black.png';
                     }elseif($rmedia == 'instagram')
                     {
                         $rsrc = URL:: asset('/assets/front-end/images/col_icon_instagram-black.png');
                     }elseif($rmedia == 'twitter')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_twitter-black.png';
                     }elseif($rmedia == 'youtube')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_youtube-black.png';
                     }elseif($rmedia == 'twitch')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_twitch-black.png';
                     }elseif($rmedia == 'tiktok')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_tiktok-black.png';
                     }else
                     {
                        $rsrc = '';
                         // $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_snapchat-black.png';
                     }
                ?>
                    <div class="col-12">
                        <div class="checkoneOn d-flex">
                            <div class="iconbox">
                                <div class="socialCnt nmt   @if(Auth::user()->status != 1) uncheck @endif"> 
                                    <img src="{{ $rsrc }}" class="iconColored" alt="">
                                </div>
                            </div>
                            <div class="inputs">
                                <div class="pricenh">
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
                <!-- <div class="col-12">
                    <div class="checkoneOn d-flex disable">
                        <div class="iconbox">
                            <div class="socialCnt"> 
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_twitter.png" class="iconColored" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="pricenh">
                                250€
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="checkoneOn d-flex disable">
                        <div class="iconbox">
                            <div class="socialCnt"> 
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_youtube.png" class="iconColored" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="pricenh">
                                250€
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="col-12">
                    <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span> <div class="dyn_pric">Active Dynamic Pricing</div>
                </div>
                <div class="col-12">
                    <div class="asdd">
                        <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span> 
                        <div class="custom-control custom-checkbox">
                        <input type="checkbox" name="" id="videoCustomerRights" value="checkedValue" class="custom-control-input"
                        @if(Auth::user()->advertisingMethodDetail && Auth::user()->advertisingMethodDetail->video_customer_right_price > 0) Checked @endif>
                        <label for="videoCustomerRights">Content exclusive rights for the brand</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-12">
                    <div class="inputs p-0">
                        <div class="form-group pricing_top">
                            <div class="input-group">
                                <span class="input-group-text" id="basic-addon1">€</span>
                                <input type="text" name="video_customer_right_price" id="videoCustomerRightPrice" placeholder="0" class="hidden-textbox form-control text-start ds" pattern="\d+\.?\d*"
                                 value="{{Auth::user()->advertisingMethodDetail ? Auth::user()->advertisingMethodDetail->video_customer_right_price : 0}}"  {{ ( Auth::user()->advertisingMethodDetail &&  ( Auth::user()->advertisingMethodDetail->video_customer_right_price > 0) )? '' : 'readonly'}} >
                            </div>
                        </div>
                    </div>
                </div> 
            </div>
        </div>
        <div class="ads_content liv-cont" data-metCont="liv">
            <div class="row">
                <div class="col-12">
                    <div class="checkone d-flex">
                        <div class="iconbox">
                            <div class="socialCnt nmt @if(Auth::user()->status != 1) uncheck @endif" "> 
                                <img src="{{$src}}" class="iconBlack" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="form-group pricing_top">
                                <label class="priceSpan">Fixed price up to <span id="livestreamMinute">{{$livestream_minutes}} </span> Minutes</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">€</span>
                                    <input type="text" name="livestream_price_{{$media}}" placeholder="0" class="hidden-textbox form-control text-start ds" 
                                    id="livestreamPrice" data-media="{{$media}}" data-followers="{{$followers}}" pattern="\d+\.?\d*" value="{{ $pricelive }}" >
                                </div>
                                <small>Depending on your followers, we suggest a price of you</small>
                                <span class="suggest_price">{{number_format($livestreamPrice,2)}} €</span>
                                <input type="hidden" name="livestream_minutes" id="totalLivestreamMinutes" value="{{$livestream_minutes}}">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="form-group pricing_top">
                                <label class="priceSpan datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i> Additional variable price per <span id="addLivestreamMinute">{{$livestream_minutes}} </span> Minutes</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">€</span>
                                    <input type="text" name="add_livestream_price_{{$media}}" placeholder="0" class="hidden-textbox form-control text-start ds" 
                                    id="addLivestreamPrice" data-media="{{$media}}" data-followers="{{$followers}}" pattern="\d+\.?\d*" value="{{$addpricelive}}" >
                                </div>
                                <small>Depending on your followers, we suggest a price of you</small>
                                <span class="suggest_price">{{number_format($addLivestreamPrice,2)}} €</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="market_place_slider">
                        <div class="placeSlidee_title"> Your price on marketplace</div>
                        <div class="place_slider">
                            <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css">
                            <div class="main">
                                <button type="button" class="btn-range" onclick="livestreamRange('minus')" id="btnRange" data-dir="minus"><i class="fa-solid fa-angle-left"></i></button>
                                <div id="storlekslider-sec"></div>
                                <!-- <input type="text" name="storlek" id="storlek_testet" /> -->
                                <button type="button" class="btn-range" onclick="livestreamRange('plus')" id="btnRange" data-dir="plus"><i class="fa-solid fa-angle-right"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
                @foreach($socialFollowerCount as $key => $social)
    
                    @if($media != $social->media)
                    <?php 
                            $newLivetreamPrice = 0;
                            $addNewLivetreamPrice = 0;
                    
                            if($social->media == 'facebook')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newLivetreamPrice = Auth::user()->advertisingMethodDetail->livestream_price_facebook;
                                    $addNewLivetreamPrice = Auth::user()->advertisingMethodDetail->add_livestream_price_facebook;
                                }
                                $srclive = URL:: asset('/').'/assets/front-end/images/icons/col_icon_facebook.png';
                            }elseif($social->media == 'instagram')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newLivetreamPrice = Auth::user()->advertisingMethodDetail->livestream_price_instagram;
                                    $addNewLivetreamPrice = Auth::user()->advertisingMethodDetail->add_livestream_price_instagram;
                                }
                                $srclive = URL:: asset('/').'/assets/front-end/images/icons/col_icon_instagram.png';
                            }elseif($social->media == 'twitter')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newLivetreamPrice = Auth::user()->advertisingMethodDetail->livestream_price_twitter;
                                    $addNewLivetreamPrice = Auth::user()->advertisingMethodDetail->add_livestream_price_twitter;
                                }
                                $srclive = URL:: asset('/').'/assets/front-end/images/icons/col_icon_twitter.png';
                            }elseif($social->media == 'youtube')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newLivetreamPrice = Auth::user()->advertisingMethodDetail->livestream_price_youtube;
                                    $addNewLivetreamPrice = Auth::user()->advertisingMethodDetail->add_livestream_price_youtube;
                                }
                                $srclive = URL:: asset('/').'/assets/front-end/images/icons/col_icon_youtube.png';
                            }elseif($social->media == 'twitch')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newLivetreamPrice = Auth::user()->advertisingMethodDetail->livestream_price_twitch;
                                    $addNewLivetreamPrice = Auth::user()->advertisingMethodDetail->add_livestream_price_twitch;
                                }
                                $srclive = URL:: asset('/').'/assets/front-end/images/icons/col_icon_twitch.png';
                            }elseif($social->media == 'tiktok')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newLivetreamPrice = Auth::user()->advertisingMethodDetail->livestream_price_tiktok;
                                    $addNewLivetreamPrice = Auth::user()->advertisingMethodDetail->add_livestream_price_tiktok;
                                }
                                $srclive = URL:: asset('/').'/assets/front-end/images/icons/col_icon_tiktok.png';
                            }
                            // elseif($social->media == 'snapchat')
                            // {
                            //     if(Auth::user()->advertisingMethodDetail != null)
                            //     {
                            //         $newLivetreamPrice = Auth::user()->advertisingMethodDetail->livestream_price_snapchat;
                            //         $addNewLivetreamPrice = Auth::user()->advertisingMethodDetail->add_livestream_price_snapchat;
                            //     }
                            //     $srclive = URL:: asset('/').'/assets/front-end/images/icons/col_icon_snapchat.png';
                            // }
                        ?>
                    <div class="col-12">
                        <div class="checkoneOn d-flex">
                            <div class="iconbox">
                                <div class="socialCnt  @if(Auth::user()->status != 1) uncheck @endif">
                                    <img src="{{$srclive}}" class="iconColored" alt="">
                                </div>
                            </div>
                            <div class="inputs">
                                <div class="pricenh" id="livestreammedia{{$social->media}}">
                                    {{number_format($newLivetreamPrice,2)}} €
                                </div>
                                <input type="hidden" id="livestreamPrice{{$social->media}}" name="livestream_price_{{$social->media}}" 
                                value="{{$newLivetreamPrice}}">
                                <input type="hidden" id="addLivestreamPrice{{$social->media}}" name="add_livestream_price_{{$social->media}}" 
                                value="{{$addNewLivetreamPrice}}">
                            </div>
                        </div>
                    </div>
                @endif
            @endforeach
            @foreach($remainingMedias as $rmedia)
                
                <?php 
                     if($rmedia == 'facebook')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_facebook-black.png';
                     }elseif($rmedia == 'instagram')
                     {
                         $rsrc = URL:: asset('/assets/front-end/images/col_icon_instagram-black.png');
                     }elseif($rmedia == 'twitter')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_twitter-black.png';
                     }elseif($rmedia == 'youtube')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_youtube-black.png';
                     }elseif($rmedia == 'twitch')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_twitch-black.png';
                     }elseif($rmedia == 'tiktok')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_tiktok-black.png';
                     }else
                     {
                        $rsrc = '';
                         // $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_snapchat-black.png';
                     }
                ?>
                    <div class="col-12">
                        <div class="checkoneOn d-flex">
                            <div class="iconbox">
                                <div class="socialCnt  @if(Auth::user()->status != 1) uncheck @endif"> 
                                    <img src="{{ $rsrc }}" class="iconColored" alt="">
                                </div>
                            </div>
                            <div class="inputs">
                                <div class="pricenh">
                                  
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
                <!-- <div class="col-12">
                    <div class="checkoneOn d-flex">
                        <div class="iconbox">
                            <div class="socialCnt"> 
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_instagram.png" class="iconColored" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="pricenh">
                                250€
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="checkoneOn d-flex disable">
                        <div class="iconbox">
                            <div class="socialCnt"> 
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_twitch.png" class="iconColored" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="pricenh">
                                250€
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="checkoneOn d-flex disable">
                        <div class="iconbox">
                            <div class="socialCnt"> 
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_youtube.png" class="iconColored" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="pricenh">
                                250€
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="col-12">
                    <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span> <div class="dyn_pric">Active Dynamic Pricing</div>
                </div>
                <div class="col-12">
                    <div class="asdd">
                        <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span> 
                        <div class="custom-control custom-checkbox">
                        <input type="checkbox" name="" id="liveCustomerRights" value="checkedValue" class="custom-control-input"
                        @if(Auth::user()->advertisingMethodDetail && Auth::user()->advertisingMethodDetail->live_customer_right_price > 0 ) checked @endif>
                        <label for="liveCustomerRights">Content exclusive rights for the customer</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-12">
                    <div class="inputs p-0">
                        <div class="form-group pricing_top">
                            <div class="input-group">
                                <span class="input-group-text" id="basic-addon1">€</span>
                                <input type="text" name="live_customer_right_price" id="liveCustomerRightPrice" placeholder="0" class="hidden-textbox form-control text-start ds" pattern="\d+\.?\d*" 
                                value="{{Auth::user()->advertisingMethodDetail ? Auth::user()->advertisingMethodDetail->live_customer_right_price : 0}}"  {{ ( Auth::user()->advertisingMethodDetail &&  ( Auth::user()->advertisingMethodDetail->live_customer_right_price > 0) )? '' : 'readonly'}}  >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="ads_content shc-cont" data-metCont="shc">
            <div class="row">
                <div class="col-12">
                    <div class="checkone d-flex">
                        <div class="iconbox">
                            <div class="socialCnt  nmt @if(Auth::user()->status != 1) uncheck @endif"> 
                                <img src="{{$src}}" class="iconBlack" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="form-group pricing_top">
                                <label class="priceSpan">Price per post</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">€</span>
                                    <input type="text" name="share_price_{{$media}}" placeholder="0" class="hidden-textbox form-control text-start ds" 
                                    id="sharePrice" data-media="{{$media}}" data-followers="{{$followers}}" pattern="\d+\.?\d*" value="{{ $priceshare }}" >
                                </div>
                                <small>Depending on your followers, we suggest a price of you</small>
                                <span class="suggest_price">{{number_format($sharePrice,2)}} €</span>
                            </div>
                        </div>
                    </div>
                </div>
                @foreach($socialFollowerCount as $key => $social)
    
                    @if($media != $social->media)
                    <?php 
                            $newSharePrice = 0;
                    
                            if($social->media == 'facebook')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newSharePrice = Auth::user()->advertisingMethodDetail->share_price_facebook;
                                }
                                $srcshare = URL:: asset('/').'/assets/front-end/images/icons/col_icon_facebook.png';
                            }elseif($social->media == 'instagram')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newSharePrice = Auth::user()->advertisingMethodDetail->share_price_instagram;
                                }
                                $srcshare = URL:: asset('/').'/assets/front-end/images/icons/col_icon_instagram.png';
                            }elseif($social->media == 'twitter')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newSharePrice = Auth::user()->advertisingMethodDetail->share_price_twitter;
                                }
                                $srcshare = URL:: asset('/').'/assets/front-end/images/icons/col_icon_twitter.png';
                            }elseif($social->media == 'youtube')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newSharePrice = Auth::user()->advertisingMethodDetail->share_price_youtube;
                                }
                                $srcshare = URL:: asset('/').'/assets/front-end/images/icons/col_icon_youtube.png';
                            }elseif($social->media == 'twitch')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newSharePrice = Auth::user()->advertisingMethodDetail->share_price_twitch;
                                }
                                $srcshare = URL:: asset('/').'/assets/front-end/images/icons/col_icon_twitch.png';
                            }elseif($social->media == 'tiktok')
                            {
                                if(Auth::user()->advertisingMethodDetail != null)
                                {
                                    $newSharePrice = Auth::user()->advertisingMethodDetail->share_price_tiktok;
                                }
                                $srcshare = URL:: asset('/').'/assets/front-end/images/icons/col_icon_tiktok.png';
                            }
                            // elseif($social->media == 'snapchat')
                            // {
                            //     if(Auth::user()->advertisingMethodDetail != null)
                            //     {
                            //         $newSharePrice = Auth::user()->advertisingMethodDetail->share_price_snapchat;
                            //     }
                            //     $srcshare = URL:: asset('/').'/assets/front-end/images/icons/col_icon_snapchat.png';
                            // }
                        ?>
                    <div class="col-12">
                        <div class="checkoneOn d-flex">
                            <div class="iconbox">
                                <div class="socialCnt nmt  @if(Auth::user()->status != 1) uncheck @endif">
                                    <img src="{{$srcshare}}" class="iconColored" alt="">
                                </div>
                            </div>
                            <div class="inputs">
                                <div class="pricenh" id="sharemedia{{$social->media}}">
                                    {{number_format($newSharePrice,2)}} €
                                </div>
                                <input type="hidden" id="sharePrice{{$social->media}}" name="share_price_{{$social->media}}" 
                                value="{{$newSharePrice}}">
                            </div>
                        </div>
                    </div>
                    @endif
                @endforeach

                @foreach($remainingMedias as $rmedia)
                
                <?php 
                     if($rmedia == 'facebook')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_facebook-black.png';
                     }elseif($rmedia == 'instagram')
                     {
                         $rsrc = URL:: asset('/assets/front-end/images/col_icon_instagram-black.png');
                     }elseif($rmedia == 'twitter')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_twitter-black.png';
                     }elseif($rmedia == 'youtube')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_youtube-black.png';
                     }elseif($rmedia == 'twitch')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_twitch-black.png';
                     }elseif($rmedia == 'tiktok')
                     {
                         $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_tiktok-black.png';
                     }else
                     {
                        $rsrc = '';
                         // $rsrc = URL:: asset('/').'/assets/front-end/images/col_icon_snapchat-black.png';
                     }
                ?>
                    <div class="col-12">
                        <div class="checkoneOn d-flex">
                            <div class="iconbox">
                                <div class="socialCnt nmt  @if(Auth::user()->status != 1) uncheck @endif"> 
                                    <img src="{{ $rsrc }}" class="iconColored" alt="">
                                </div>
                            </div>
                            <div class="inputs">
                                <div class="pricenh">
                                  
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
                <!-- <div class="col-12">
                    <div class="market_place_slider">
                        <div class="placeSlidee_title"> Your price on marketplace</div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="checkoneOn d-flex">
                        <div class="iconbox">
                            <div class="socialCnt">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_facebook.png" class="iconColored" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="pricenh">
                                250€
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="checkoneOn d-flex">
                        <div class="iconbox">
                            <div class="socialCnt"> 
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_instagram.png" class="iconColored" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="pricenh">
                                250€
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="checkoneOn d-flex disable">
                        <div class="iconbox">
                            <div class="socialCnt"> 
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_twitter.png" class="iconColored" alt="">
                            </div>
                        </div>
                        <div class="inputs">
                            <div class="pricenh">
                                250€
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="col-12">
                    <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span> <div class="dyn_pric">Active Dynamic Pricing</div>
                </div>
                <div class="col-12">
                    <div class="asdd">
                        <span class="ads_link_info datatip" data-tip="Lorem ipsum dolor sit amet, consectetur adipiscing elit"><i class="fa-solid fa-circle-info"></i></span> 
                        <div class="custom-control custom-checkbox">
                        <input type="checkbox" name="" id="shareCustomerRights" value="checkedValue" class="custom-control-input"
                        @if (Auth::user()->advertisingMethodDetail && Auth::user()->advertisingMethodDetail->share_customer_right_price > 0 ) Checked @endif>
                        <label for="shareCustomerRights">Content exclusive rights for the customer</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-12">
                    <div class="inputs p-0">
                        <div class="form-group pricing_top">
                            <div class="input-group">
                                <span class="input-group-text" id="basic-addon1">€</span>
                                <input type="text" name="share_customer_right_price" id="shareCustomerRightPrice" placeholder="0" class="hidden-textbox form-control text-start ds" pattern="\d+\.?\d*" value="{{Auth::user()->advertisingMethodDetail ? Auth::user()->advertisingMethodDetail->share_customer_right_price : 0}}"  {{ ( Auth::user()->advertisingMethodDetail &&  ( Auth::user()->advertisingMethodDetail->share_customer_right_price > 0) )? '' : 'readonly'}} >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" name="advertising_method_price" value="true">
 </div>

<script type="text/javascript">
$(document).ready(function () { 

    $('.flexCheckDefault').on('click', function () {
        if ($(this).prop('checked')) {
            $(this).parent().parent().nextAll().find('input:checkbox').prop('disabled', false);
            $(this).parent().parent().nextAll().find('input:text').prop('readOnly', false);
            $(this).parent().parent().nextAll().find('input:text').attr('min', 1);
            $(this).parent().parent().nextAll().find('input:text').val(0); 
            $(this).parent().parent().nextAll().find('input:checkbox').prop('checked', true);
        } else {
            $(this).parent().parent().nextAll().find('input:checkbox').prop('disabled', true);
            $(this).parent().parent().nextAll().find('input:text').prop('readOnly', true);
            $(this).parent().parent().nextAll().find('input:text').attr('min', 0);
            $(this).parent().parent().nextAll().find('input:text').val(0); 
            $(this).parent().parent().nextAll().find('input:checkbox').prop('checked', false);
        }
    });

    $('.hidden-textbox').on('input', function (event) { 
        this.value = this.value.replace(/[^0-9]/g, '');
    });
});

$("#slide").on('change',function () {
 var value = $(this).val();
    $("#img").width(value);
    $("#img").height(value);
   // console.log(value);
});
$('.maxTime').click(function() {
    //if value < max
    $("#slide").val(parseInt($("#slide").val())+5);  
    $("#slide").trigger('change');
});
$('.minTime').click(function() {
    //if value < max
    $("#slide").val(parseInt($("#slide").val())-5);  
    $("#slide").trigger('change');
});


var minutes = @json($minutes);
var livestream_minutes = @json($livestream_minutes);

$( "#storlekslider" ).slider({
    range: "max",
    min: 0,
    max: 120,
    step: 1,
    value: minutes    ,
    slide: function( event, ui ) {
    //var value1 = $("#storlekslider").slider("value");  
    $("#storlek_testet").val( ui.value );
    $(ui.value).val(minutes);
    var value1 = $("#storlek_testet").val();
    $("#storlekslider").find(".ui-slider-handle").text(minutes);  

    }
    });
$("#storlekslider").find(".ui-slider-handle").text(minutes +' minutes')
$("#storlek_testet").keyup(function() {
    $("#storlekslider").slider("value" , $(this).val());
  var value1 = $("#storlek_testet").val();
    $("#storlekslider").find(".ui-slider-handle").text(value1);
});

function videoRange(action){
// $('#btnRange1').click(function() {
  var direction = action;
  var value =  $("#storlekslider").slider("value");
  if (direction == "plus") {
     $("#storlekslider").slider("value", value+5);
  } else {
    $("#storlekslider").slider("value", value-5);
  }
  var currentVal = $("#storlekslider").slider("value");
  $("#storlek_testet").val(currentVal);
  $("#storlekslider").find(".ui-slider-handle").text(currentVal+" Minutes");
  $("#videoMinute").text(currentVal);
  $('#addVideoMinute').text(currentVal);
  $('#totalVideoMinutes').val(currentVal);

// });
}


$( "#storlekslider-sec" ).slider({
    range: "max",
    min: 0,
    max: 120,
    step: 1,
    value: livestream_minutes,
    slide: function( event, ui ) {
    //var value1 = $("#storlekslider-sec").slider("value");  
    $("#storlek_testet-sec").val( ui.value );
    $(ui.value).val(livestream_minutes);
    var value2 = $("#storlek_testet-sec").val();
    $("#storlekslider-sec").find(".ui-slider-handle").text(livestream_minutes);  
    }
    });
$("#storlekslider-sec").find(".ui-slider-handle").text(livestream_minutes+" Minutes")
$("#storlek_testet-sec").keyup(function() {
    $("#storlekslider-sec").slider("value" , $(this).val());
  var value2 = $("#storlek_testet-sec").val();
    $("#storlekslider-sec").find(".ui-slider-handle").text(value2);
});

function livestreamRange(action){

// $('#btnRange').click(function() {
  var direction2 = action;
  var value_nom =  $("#storlekslider-sec").slider("value");
  if (direction2 == "plus") {
     $("#storlekslider-sec").slider("value", value_nom+5);
  } else {
    $("#storlekslider-sec").slider("value", value_nom-5);
  }
  var currentVal2 = $("#storlekslider-sec").slider("value");
  $("#storlek_testet-sec").val(currentVal2);
  $("#storlekslider-sec").find(".ui-slider-handle").text(currentVal2+" Minutes");

  $("#livestreamMinute").text(currentVal2);
  $('#addLivestreamMinute').text(currentVal2);
  $('#totalLivestreamMinutes').val(currentVal2);
// });
}

$(document).on("click", ".ads_type", function(){
    $(".ads_link").removeClass("active");
    $(this).closest(".ads_link").addClass("active");
    var data_metLink = $(this).attr("data-metLink");
    var data_metCont = $(".ads_content").attr("data-metCont");
    $(".ads_content").removeClass("current");
    $(".ads_content[data-metCont="+data_metLink+"]").addClass("current");
})

$('#videoPrice').keyup(function(){
    setTimeout(function () {
        var price = $('#videoPrice').val();
        var media = $('#videoPrice').data('media');
        var followers = $('#videoPrice').data('followers');
        var pricePerFollower = parseFloat(price) / parseInt(followers);

        var socialCount = @json($socialFollowerCount);
        
        Object.keys(socialCount).forEach(key => {
            calculatePrice(socialCount, key);
        });
        
        function calculatePrice(item, index) {
            if(media != item[index]['media']){
                var follower = item[index]['followers'];
                var followerprice = parseFloat(pricePerFollower) * parseInt(follower);
                
                $('#media'+item[index]['media']).text(followerprice.toFixed(2)+' €');
                $('#videoPrice'+item[index]['media']).val(followerprice.toFixed(2));
            }
        }
    }, 1000)
});

$('#addVideoPrice').keyup(function(){
    setTimeout(function () {
        var price = $('#addVideoPrice').val();
        var media = $('#addVideoPrice').data('media');
        var followers = $('#addVideoPrice').data('followers');
        var pricePerFollower = parseFloat(price) / parseInt(followers);

        var socialCount = @json($socialFollowerCount);
        
        Object.keys(socialCount).forEach(key => {
            calculateAddPrice(socialCount, key);
        });
        
        function calculateAddPrice(item, index) {
            if(media != item[index]['media']){
                var follower = item[index]['followers'];
                var followerprice = parseFloat(pricePerFollower) * parseInt(follower);
                
                // $('#media'+item[index]['media']).text(followerprice.toFixed(2));
                $('#addVideoPrice'+item[index]['media']).val(followerprice.toFixed(2));
            }
        }
    }, 1000)
});

$('#livestreamPrice').keyup(function(){
    setTimeout(function () {
        var price = $('#livestreamPrice').val();
        var media = $('#livestreamPrice').data('media');
        var followers = $('#livestreamPrice').data('followers');
        var pricePerFollower = parseFloat(price) / parseInt(followers);

        var socialCount = @json($socialFollowerCount);
        
        Object.keys(socialCount).forEach(key => {
            calculatePrice(socialCount, key);
        });
        
        function calculatePrice(item, index) {
            if(media != item[index]['media']){
                var follower = item[index]['followers'];
                var followerprice = parseFloat(pricePerFollower) * parseInt(follower);
                
                $('#livestreammedia'+item[index]['media']).text(followerprice.toFixed(2)+' €');
                $('#livestreamPrice'+item[index]['media']).val(followerprice.toFixed(2));
            }
        }
    }, 1000)
});

$('#addLivestreamPrice').keyup(function(){
    setTimeout(function () {
        var price = $('#addLivestreamPrice').val();
        var media = $('#addLivestreamPrice').data('media');
        var followers = $('#addLivestreamPrice').data('followers');
        var pricePerFollower = parseFloat(price) / parseInt(followers);

        var socialCount = @json($socialFollowerCount);
        
        Object.keys(socialCount).forEach(key => {
            calculateAddPrice(socialCount, key);
        });
        
        function calculateAddPrice(item, index) {
            if(media != item[index]['media']){
                var follower = item[index]['followers'];
                var followerprice = parseFloat(pricePerFollower) * parseInt(follower);
                
                // $('#media'+item[index]['media']).text(followerprice.toFixed(2));
                $('#addLivestreamPrice'+item[index]['media']).val(followerprice.toFixed(2));
            }
        }
    }, 1000)
});

$('#sharePrice').keyup(function(){
    setTimeout(function () {
        var price = $('#sharePrice').val();
        var media = $('#sharePrice').data('media');
        var followers = $('#sharePrice').data('followers');
        var pricePerFollower = parseFloat(price) / parseInt(followers);

        var socialCount = @json($socialFollowerCount);
        
        Object.keys(socialCount).forEach(key => {
            calculatePrice(socialCount, key);
        });
        
        function calculatePrice(item, index) {
            if(media != item[index]['media']){
                var follower = item[index]['followers'];
                var followerprice = parseFloat(pricePerFollower) * parseInt(follower);
                
                $('#sharemedia'+item[index]['media']).text(followerprice.toFixed(2)+' €');
                $('#sharePrice'+item[index]['media']).val(followerprice.toFixed(2));
            }
        }
    }, 1000)
});

$('#shareCustomerRights').click(function(){
    if($(this).is(':checked') == true)
    {
        $('#shareCustomerRightPrice').removeAttr('readonly');
    }else{
        $('#shareCustomerRightPrice').attr('readonly', '');
    }
});

$('#videoCustomerRights').click(function(){
    if($(this).is(':checked') == true)
    {
        $('#videoCustomerRightPrice').removeAttr('readonly');
    }else{
        $('#videoCustomerRightPrice').attr('readonly', '');
    }
});

$('#liveCustomerRights').click(function(){
    if($(this).is(':checked') == true)
    {
        $('#liveCustomerRightPrice').removeAttr('readonly');
    }else{
        $('#liveCustomerRightPrice').attr('readonly', '');
    }
});
</script>