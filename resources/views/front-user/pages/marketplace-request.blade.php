   <div class="modal fade influncer" id="requestDialogue" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestDialogueLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="wizardHeading">Order Form</div>


                        <!-- Selected users -->
                        <div class="col-xl-12 col-md-12">
                            <div class="steps selectedUserOuter">
                                <div class="markeplaceRequest">
                                    <div class="orderFormDiv">
                                        <div class="selectedUser p-0" id="selectedUserPopup">
                                                @php $totalFollowers = 0 ;$totalPrice = 0 ; @endphp
                                            @if(isset($influencerData ) && isset($media))
                                                @foreach($influencerData as $row)
                                                    @if($row->request == '1' )
                                                        @php $totalFollowers = $totalFollowers + $row->followers ; @endphp
                                                        <div class="userDetails newClass{{$row->i_id}}" id="{{$row->i_id}}">
                                                            <div class="userDetails1 d-flex">
                                                                <a href="{{$row->url}}" target="_blank">
                                                                    <span class="handelname">{{$row->username}} </span>
                                                                </a>
                                                                <span class="handelpletform">
                                                                    <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{$media}}.svg" class="small-icon" alt="">
                                                                </span>
                                                            </div>

                                                            <div class="userDetails2">
                                                                <div class="userDetailImage">
                                                                    <a href="{{$row->url}}" target="_blank">
                                                                        <img src="{{asset('storage/' . $row->picture}}" class="iconColored" alt="">
                                                                    </a>
                                                                </div>
                                                                <div class="userDetailInfo">
                                                                    <span class="infoName">{{$row->followers}} Follower</span>
                                                                    <span class="infoStar">
                                                                        <i class="fa-solid fa-star"></i>
                                                                        <i class="fa-solid fa-star"></i>
                                                                        <i class="fa-solid fa-star"></i>
                                                                        <i class="fa-regular fa-star"></i>
                                                                        <i class="fa-regular fa-star"></i>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div class="userDetails3">
                                                                <div class="multiData">
                                                                    <div class="group-Data">
                                                                        <div class="group-Data-left">
                                                                            Target Age
                                                                        </div>
                                                                        <div class="group-Data-right">
                                                                            {{$row->ages}}
                                                                        </div>
                                                                    </div>
                                                                    <div class="group-Data">
                                                                        <div class="group-Data-left">
                                                                            Target Language
                                                                        </div>
                                                                        <div class="group-Data-right">
                                                                            {{$row->content_language}}
                                                                        </div>
                                                                    </div>
                                                                    <div class="group-Data">
                                                                        <div class="group-Data-left">
                                                                            Target Gender
                                                                        </div>
                                                                        <div class="group-Data-right">
                                                                            {{$row->content_attracts}}
                                                                        </div>
                                                                    </div>
                                                                    <div class="group-Data">
                                                                        <div class="group-Data-left">
                                                                            HashTags
                                                                        </div>
                                                                        <div class="group-Data-right drp">
                                                                            @if(isset($row->tags))
                                                                            @foreach($row->tags as $tags)
                                                                            <span>#{{$tags->tags}}</span>
                                                                            @endforeach
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                    <div class="group-Data">
                                                                        <div class="group-Data-left">
                                                                            Influencer Type
                                                                        </div>
                                                                        <div class="group-Data-right">
                                                                            {{$row->influencer_type}}
                                                                        </div>
                                                                    </div>
                                                                    <div class="group-Data">
                                                                        <div class="group-Data-left">
                                                                            Influencer Gender
                                                                        </div>
                                                                        <div class="group-Data-right">
                                                                            @if($row->influencer_type !='Content page') {{$row->gender}} @endif
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="userDetails4">

                                                                <div class="btn-group checkedNameBox" role="group" data-bs-toggle="buttons">

                                                                    <a href="javascript:void(0)" class="removeButtonStyle">Remove</a>

                                                                    <div class="confirmBox">
                                                                        <div class="confirmOverlay"></div>
                                                                        <div class="confirmInner">
                                                                            <div class="wizardHeading">Remove Influencer</div>
                                                                            <div class="confirmText">
                                                                                Do you really want to delete influencer
                                                                            </div>
                                                                            <div class="confirmationButton">
                                                                                <a name="" class="btn btn-danger remove" href="javascript:void(0)" role="button"  id="{{$row->i_id}}" >Confirm</a>
                                                                                <a href="javascript:void(0)" class="close-confirmation">
                                                                                    Close
                                                                                </a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <span class="infoPrice">
                                                                    <?php
                                                                    $newLivetreamPrice = 0;
                                                                    $addNewLivetreamPrice = 0;

                                                                    $user = App\Models\User::find($row->i_user_id);

                                                                    $fieldName = $advertising.'_price';
                                                                    if($user->advertisingMethodPrice != null)
                                                                    {
                                                                        $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                                    }
                                                                    ?>
                                                                    @php   $totalPrice = $totalPrice + $newLivetreamPrice ; @endphp
                                                                      {{number_format($newLivetreamPrice,2)}} €
                                                                </span>
                                                            </div>
                                                            <input type="hidden" name="" value="{{$newLivetreamPrice}}" class="price{{$row->i_id}}">
                                                            <input type="hidden" name="" value="{{$row->followers}}" class="followers{{$row->i_id}}">
                                                        </div>
                                                    @endif
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                    @if(isset($totalCount))
                                    <div class="influncerUpdate"  @if($totalCount!=0) style="display:block;" @else style="display:none;"   @endif  >
                                        <div><span class="totalCount">{{ isset($totalCount)?$totalCount:0 }}</span> Influencers</div>
                                        <div>Total Reach: <span class="totalFollowers">{{ isset($totalFollowers)?$totalFollowers:0 }}</span> Followers</div>
                                        <div>Total Cost:   <span class="totalPrice">{{ isset($totalPrice)?number_format($totalPrice,2):0 }}</span> €</div>
                                    </div>
                                    @endif



                                    @if(isset($influencerData ) && isset($media) && isset($advertising))
                                        <div class="d-flex flex-wrap flexUserOuter">
                                            @foreach($influencerData as $row)
                                                @php $req_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id',$row->i_id)->count();  @endphp

                                                @if($req_count < 5)
                                                <div class="flexUser data_{{$row->i_id}}" id="data_{{$row->i_id}}" style="display: block;">
                                                    <div class="userDetails newClass{{$row->i_id}}" id="{{$row->i_id}}"    >
                                                        <div class="userDetails1 d-flex">
                                                            <a href="{{$row->url}}" target="_blank">
                                                                <span class="handelname">{{$row->username}} {{$row->request}}</span>
                                                            </a>
                                                            <span class="handelpletform">
                                                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{isset($media)?$media:''}}.svg" class="small-icon" alt="">
                                                            </span>
                                                        </div>

                                                        <div class="userDetails2">
                                                            <div class="userDetailImage">
                                                                <a href="{{$row->url}}" target="_blank">
                                                                    <img src="{{asset('storage/' . $row->picture}}" class="iconColored" alt="">
                                                                </a>
                                                            </div>
                                                            <div class="userDetailInfo">
                                                                <span class="infoName">{{$row->followers}} Follower</span>
                                                                <span class="infoStar">
                                                                    <i class="fa-solid fa-star"></i>
                                                                    <i class="fa-solid fa-star"></i>
                                                                    <i class="fa-solid fa-star"></i>
                                                                    <i class="fa-regular fa-star"></i>
                                                                    <i class="fa-regular fa-star"></i>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="userDetails3">
                                                            <div class="multiData">
                                                                <div class="group-Data">
                                                                    <div class="group-Data-left">
                                                                        Target Age
                                                                    </div>
                                                                    <div class="group-Data-right">
                                                                        {{$row->ages}}
                                                                    </div>
                                                                </div>
                                                                <div class="group-Data">
                                                                    <div class="group-Data-left">
                                                                        Target Language
                                                                    </div>
                                                                    <div class="group-Data-right">
                                                                        {{$row->content_language}}
                                                                    </div>
                                                                </div>
                                                                <div class="group-Data">
                                                                    <div class="group-Data-left">
                                                                        Target Gender
                                                                    </div>
                                                                    <div class="group-Data-right">
                                                                        {{$row->content_attracts}}
                                                                    </div>
                                                                </div>
                                                                <div class="group-Data">
                                                                    <div class="group-Data-left">
                                                                        HashTags
                                                                    </div>
                                                                    <div class="group-Data-right drp">
                                                                        @if(isset($row->tags))
                                                                        @foreach($row->tags as $tags)
                                                                        <span>#{{$tags->tags}}</span>
                                                                        @endforeach
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                               <div class="group-Data">
                                                                    <div class="group-Data-left">
                                                                        Influencer Type
                                                                    </div>
                                                                    <div class="group-Data-right">
                                                                        {{$row->influencer_type}}
                                                                    </div>
                                                                </div>
                                                                <div class="group-Data">
                                                                    <div class="group-Data-left">
                                                                        Influencer Gender
                                                                    </div>
                                                                    <div class="group-Data-right">
                                                                        @if($row->influencer_type !='Content page') {{$row->gender}} @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="userDetails4">

                                                            <div class="btn-group checkedNameBox" role="group" data-bs-toggle="buttons">
                                                                <label class="checkboxAllCheck d-block">
                                                                    <input id="selectUser{{$row->i_id}}" type="checkbox" name="seclectCard" class="Checkall">
                                                                    <span class="span{{$row->i_id}}" ><span class="selcc d-block">Add</span></span>
                                                                </label>
                                                                <a href="javascript:void(0)" class="removeButtonStyle">Remove</a>

                                                                    <div class="confirmBox">
                                                                        <div class="confirmOverlay"></div>
                                                                        <div class="confirmInner">
                                                                            <div class="wizardHeading">Remove Influencer</div>
                                                                            <div class="confirmText">
                                                                                Do you really want to delete influencer
                                                                            </div>
                                                                            <div class="confirmationButton">
                                                                                <a name="" class="btn btn-danger remove" href="javascript:void(0)" role="button"  id="{{$row->i_id}}" >Confirm</a>
                                                                                <a href="javascript:void(0)" class="close-confirmation">
                                                                                    Close
                                                                                </a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                            </div>
                                                            <span class="infoPrice">
                                                                <?php
                                                                    $newLivetreamPrice = 0;
                                                                    $addNewLivetreamPrice = 0;

                                                                    $user = App\Models\User::find($row->i_user_id);

                                                                    $fieldName = $advertising.'_price';
                                                                    if($user->advertisingMethodPrice != null)
                                                                    {
                                                                        $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                                    }
                                                                    ?>
                                                                      {{number_format($newLivetreamPrice,2)}} €
                                                            </span>
                                                        </div>
                                                        <input type="hidden" name="" value="{{$newLivetreamPrice}}" class="price{{$row->i_id}}">
                                                        <input type="hidden" name="" value="{{$row->followers}}" class="followers{{$row->i_id}}">
                                                    </div>
                                                </div>
                                            @endif
                                            @endforeach
                                        </div>
                                    @endif

                                </div>
                            </div>
                        </div>
                        <div class="wizardForm">
                            <form method="post" id="order-form"  action="{{ url('/request-now')}}"  data-parsley-validate>
                                @csrf
                                <input type="hidden" name="advertising" id="advertising"  value="{{isset($advertising)?$advertising:''}}"  >
                                <input type="hidden" name="media" id="media" value="{{isset($media)?$media:''}}" >
                                <input type="hidden" name="categories" id="categories" value="{{isset($categories)?$categories:''}}" >
                                <input type="hidden" name="type" id="type" value="{{isset($type)?$type:''}}" >
                                <div class="col-xl-12 col-md-12">

                                    <h2 class="gnrlInformation">Product Information</h2>
                                    <div class="row">

                                        <input class="form-control" type="hidden" name="compaign_id" id="compaign_id" required value="{{ rand(999,9999)}}" readonly>
                                        <div class="col-md-6 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>Compaign Title</label>
                                                <input class="form-control" type="text" name="compaign_title" id="compaign_title"  >
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>Product/Service Name</label>
                                                <input class="form-control" type="text" name="name" id="name" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>Online store link</label>
                                                <input class="form-control" type="text" name="link" id="link">
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>Should the influencer publish the Online Store Link ?</label>
                                                <div class="tasklistinput">
                                                    <div class="taslListCustomRatio">
                                                        <input id="publish_yes" type="radio" name="publish" value="Yes">
                                                        <label for="publish_yes">yes</label>
                                                    </div>
                                                    <div class="taslListCustomRatio">
                                                        <input id="publish_no" type="radio" name="publish" value="No">
                                                        <label for="publish_no">No</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="floating-label smalSpace">
                                                <label>Short Product/Service Description</label>
                                                <textarea class="form-control textarea" name="short_product" id="name"></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-12">
                                            <label>Following Hashtags/ Mentions should be used by the influencer</label>
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="floating-label smalSpace">
                                                        <div class="input-group">
                                                            <span class="input-group-text" id="basic-addon1">#</span>
                                                            <input class="form-control socllink" type="text" name="hashtags" id="name" value="#">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="floating-label smalSpace">
                                                        <div class="input-group">
                                                            <span class="input-group-text" id="basic-addon1">@</span>
                                                            <input class="form-control socllink" type="text" name="mentions" id="name" value="@">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>Following Social Media OR Website should be linked by the influencer</label>
                                                <input class="form-control" type="text" name="social" id="social">
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>Which sub-category describes your product <br/>the most</label>
                                                <input class="form-control" type="text" name="subcategory" id="subcategory">
                                            </div>
                                        </div>
                                        <!-- <div class="col-md-6 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>How much time do you give the influencer to react (days)</label>
                                                <select class="form-control select" name="time" id="time">
                                                    <option>3</option>
                                                    <option>5</option>
                                                    <option>7</option>
                                                </select>
                                            </div>
                                        </div> -->
                                        <input type="hidden" name="time" value="7">
                                        <div class="col-12">
                                            <div class="floating-label smalSpace">
                                                <label>By supporting us with a hashtag, you can get 5% discount on your service-fee</label>
                                                <div class="d-flex align-items-center">
                                                    <div class="customCheck">
                                                        <input type="checkbox" name="support" id="support">
                                                        <label for="support">Accept</label>
                                                    </div>
                                                    <input class="form-control" type="text" name="site" id="site" value="#ClickItFame.com" readonly disabled>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>Your current price (Incl {{isset($AdminComission)?$AdminComission->customer:10}}% platform fee)</label>
                                                <input class="form-control" type="hidden"  id="total_price" value="{{$totalPrice}}" readonly>
                                                <input class="form-control" type="text" name="current_price" id="current_price" value="{{$totalPrice + ( ($totalPrice*intval(isset($AdminComission)?$AdminComission->customer:10))/100 )}}" readonly>
                                                <input class="form-control" type="hidden" name="admin_comission" id="admin_comission" value="{{isset($AdminComission)?$AdminComission->customer:10}}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="floating-label smalSpace">
                                                <label>With the discount</label>
                                                <div class="input-group">
                                                    <span class="input-group-text" id="basic-addon1">€</span>
                                                    <input class="form-control" type="text" name="discount_price" id="discount_price">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <!-- Selected users //-->
                                <div class="popup2btns d-flex">
                                    <a href="#" class="et-submit bg-error mx-2" data-bs-dismiss="modal" aria-label="Close">Back to Marketplace</a>
                                    <input class="et-submit mx-2" type="submit" name="confirm"   value="Confirm">
                                </div>
                            </form>
                        </div>

                </div>
            </div>
        </div>
    </div>
    <div class="loaderss" id="pageLoader">
        <img src="{{ asset('/') }}/assets/front-end/images/loading-loading-forever.gif" alt="">
    </div>
    <script>
        // $(".select").selct2();
        // $(document).on("click", ".requestDialogue", function(){
        //     var get_page_id = $(".selectedUser .userDetails").attr("id")
        //     $(".modal .flexUserOuter #data_"+get_page_id).hide();
        // })
        $(document).on('click', '.remove', function(event) {
            let idName=$(this).attr('id');
            $(".modal .flexUserOuter #data_"+idName).show();
            $(".modal .flexUserOuter #data_"+idName+" #selectUser"+idName).prop("checked", false);
            $("#selectedUser >  #"+idName).remove();
        })
        $(document).on("click", ".removeButtonStyle", function(){
            let idName=$(this).closest(".userDetails").attr('id');
            $(this).closest(".userDetails").find(".confirmBox").show();
            $(".modal .flexUserOuter #data_"+idName+" #selectUser"+idName).prop("checked", false);
        })
        $(document).on("click", ".close-confirmation", function(){
            $(this).closest(".confirmBox").hide();
        })
        $("#order-form").submit(function(){
            $("#pageLoader").show();
        })
    </script>
