<div class="markeplaceUserSidebar">
    @include('front-user.pages.marketplace-influencer-sidebar')
    </div>
    @if(isset($advertising))
    <div class="markeplaceUserCheck">
        <!-- Users -->
        <div class="divOnet">
            <div class="markeplaceUsers ">
                <div class="row">
                    <div class="col-12">
                        <div class="steps mb-3">
                            <div class="contentAllWz">
                                <div class="vizrdSelection">
                                    <div class="sst">
                                        <label class="custom-control custom-checkbox">
                                            <input type="checkbox" name="" id="" value="checkedValue" class="custom-control-input" checked disabled>
                                            <span class="custom-control-indicator"></span>
                                        </label>
                                        {{ isset($media)?ucfirst($media):'' }}
                                    </div>
                                    <div class="sst">
                                        <label class="custom-control custom-checkbox">
                                            <input type="checkbox" name="" id="" value="checkedValue" class="custom-control-input" checked disabled>
                                            <span class="custom-control-indicator">

                                            </span>
                                        </label>
                                        @php $category ='';
                                        if(isset($categories)) {
                                            foreach($categories as $cat)  $category .= $cat->name .','  ;
                                        @endphp
                                            {{ rtrim($category,',') }}
                                        @php } @endphp
                                    </div>
                                    <div class="sst">
                                        <label class="custom-control custom-checkbox">
                                            <input type="checkbox" name="" id="" value="checkedValue" class="custom-control-input" checked disabled>
                                            <span class="custom-control-indicator"></span>
                                        </label>
                                        {{ isset($advertising)?ucfirst($advertising):'' }}
                                    </div>
                                </div>
                                <div class="openFilterBtn d-block d-lg-none">Filter</div>
                                <a name="" id="" class="table-btn green-btn openWizard ms-auto" href="#" onclick="buttonClick()" role="button">Configure Wizard</a>
                            </div>
                        </div>
                    </div>
                </div>
                @if(isset($influencerDetails ))
                <div class="d-flex flex-wrap flexUserOuter">
                    @foreach($influencerDetails as $row)
                        @php $req_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id',$row->i_id)
                                        ->where(function ($query) {
                                            $query->where('review', '=', NULL)
                                                  ->orWhere('review', '=', 0);
                                        })
                                        ->where('refund_reason',NULL)->where('finish',NULL)->count();
                        @endphp

                        @if($req_count < 5)
                        @php $media_name = $advertising.'_media' ;

                        $media =  \App\Models\SocialConnect::where('user_id',$row->user_id)->where('media', $row->$media_name)->first(); @endphp

                        <div class="flexUser data_{{$row->i_id}}  @if($row->request == '1') selectBox  @endif flexUserConfig"  @if($row->request == '1') asas style="display:none;" @endif>
                            <div class="userDetails newClass{{$row->i_id}}" id="{{$row->i_id}}">
                                <div class="userDetails1 d-flex">
                                    <a href="{{$row->url}}" target="_blank">
                                        <span class="handelname">{{$row->username}} </span>
                                    </a>
                                    <span class="handelpletform">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{$media->media}}.svg" class="small-icon" alt="">
                                    </span>
                                </div>

                                <div class="userDetails2">
                                    <div class="userDetailImage">
                                        <a href="{{$media->url}}" target="_blank">
                                            <img src="{{asset('storage/' . $media->picture}}" class="iconColored" alt="">
                                        </a>
                                    </div>
                                    <div class="userDetailInfo">
                                        <span class="infoName">{{$row->followers}} Follower</span>
                                        <span class="infoStar">
                                            <i class="fa-solid fa-star"></i>
                                            <i class="fa-solid fa-star"></i>
                                            <i class="fa-solid fa-star"></i>
                                            <i class="fa-regular fa-star"></i>
                                            <i class="fa-regular fa-star"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="userDetails3">
                                    {{-- <div class="targetHeading">Target groups</div> --}}
                                    <div class="multiData">
                                        <div class="group-Data">
                                            <div class="group-Data-left">
                                                Target Age
                                            </div>
                                            <div class="group-Data-right">
                                                {{$row->ages}}
                                            </div>
                                        </div>
                                        <div class="group-Data">
                                            <div class="group-Data-left">
                                                Target Language
                                            </div>
                                            <div class="group-Data-right">
                                                {{$row->content_language}}
                                            </div>
                                        </div>
                                        <div class="group-Data">
                                            <div class="group-Data-left">
                                                Target Gender
                                            </div>
                                            <div class="group-Data-right">
                                                {{$row->content_attracts}}
                                            </div>
                                        </div>
                                        <div class="group-Data">
                                            <div class="group-Data-left">
                                                HashTags
                                            </div>
                                            <div class="group-Data-right drp">
                                                @if(isset($row->tags))
                                                @foreach($row->tags as $tags)
                                                <span>#{{$tags->tags}}</span>
                                                @endforeach
                                                @endif
                                            </div>
                                        </div>
                                        <div class="group-Data">
                                            <div class="group-Data-left">
                                                Influencer Type
                                            </div>
                                            <div class="group-Data-right">
                                                {{$row->influencer_type}}
                                            </div>
                                        </div>
                                        <div class="group-Data">
                                            <div class="group-Data-left">
                                                Influencer Gender
                                            </div>
                                            <div class="group-Data-right">
                                                @if($row->influencer_type !='Content page') {{$row->gender}} @endif
                                            </div>
                                        </div>
                                    </div>
                                    {{-- <div class="targetgroups d-flex flex-wrap">
                                        <div class="targetgroup">Age <br>
                                            <strong>{{$row->ages}}</strong>
                                        </div>
                                        <div class="targetgroup">Language<br>
                                            <strong>{{$row->content_language}}</strong>
                                        </div>
                                        <div class="targetgroup">Gender<br>
                                            <strong>{{$row->content_attracts}}</strong>
                                        </div>
                                    </div>
                                    <div class="targetgroup forTag">
                                        @if(isset($row->tags))
                                        @foreach($row->tags as $tags)
                                        <span>#{{$tags->tags}}</span>
                                        @endforeach
                                        @endif
                                    </div> --}}
                                </div>
                                <div class="userDetails4">
                                    <div class="btn-group checkedNameBox" role="group" data-bs-toggle="buttons">
                                        <label class="checkboxAllCheck">
                                            <input id="selectUser1" type="checkbox" name="seclectCard" class="Checkall">
                                            <span class="span{{$row->i_id}}" ><span class="selcc">Select</span></span>
                                        </label>
                                        <span class="handelpletform{{$row->i_id}}"></span>
                                    </div>
                                    <span class="infoPrice">
                                    <?php
                                            $newLivetreamPrice = 0;
                                            $addNewLivetreamPrice = 0;

                                            $user = App\Models\User::find($row->i_user_id);

                                            $fieldName = $advertising.'_price';
                                            if($user->advertisingMethodPrice != null)
                                            {
                                                $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                            }
                                        ?>
                                          {{number_format($newLivetreamPrice,2)}} €
                                    </span>

                                </div>
                                <input type="hidden" name="" value="{{$newLivetreamPrice}}" class="price{{$row->i_id}}">
                                <input type="hidden" name="" value="{{$row->followers}}" class="followers{{$row->i_id}}">

                            </div>
                        </div>
                        @endif
                    @endforeach
                </div>
                <div class="seemore" id="seeMore">See more</div>
                @endif
            </div>
        </div>


        <!-- Users //-->
        <!-- Selected users -->
        <div class="divSeco">
            <div class="steps selectedUserOuter">
                <div class="SelectedUserOpen">Selected User <span id="userCnt"></span></div>
                <div class="markeplaceRequest">
                    <div class="selectedUser" id="selectedUser">
                        @if(isset($influencerData ))
                            @php $totalFollowers = 0 ;$totalPrice = 0 ; @endphp
                            @foreach($influencerData as $row)
                                @if($row->request == '1' )
                                    @php $totalFollowers = $totalFollowers + $row->followers ; @endphp
                                    @php $totalPrice = $totalPrice + $row->price ; @endphp
                                    <div class="userDetails newClass{{$row->i_id}}" id="{{$row->i_id}}">
                                        <div class="userDetails1 d-flex">
                                            <a href="{{$row->url}}" target="_blank">
                                                <span class="handelname">{{$row->username}} </span>
                                            </a>
                                            <span class="handelpletform">
                                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{$row->media}}.svg" alt="">
                                            </span>
                                        </div>

                                        <div class="userDetails2">
                                            <div class="userDetailImage">
                                                <a href="{{$row->url}}" target="_blank">
                                                    <img src="{{asset('storage/' . $row->picture}}" class="iconColored" alt="">
                                                </a>
                                            </div>
                                            <div class="userDetailInfo">
                                                <span class="infoName">{{$row->followers}} Follower</span>
                                                <span class="infoStar">
                                                    <i class="fa-solid fa-star"></i>
                                                    <i class="fa-solid fa-star"></i>
                                                    <i class="fa-solid fa-star"></i>
                                                    <i class="fa-regular fa-star"></i>
                                                    <i class="fa-regular fa-star"></i>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="userDetails3">
                                            <div class="targetHeading">Target groups</div>
                                            <div class="targetgroups d-flex flex-wrap">
                                                <div class="targetgroup">Age <br>
                                                    <strong>{{$row->ages}}</strong>
                                                </div>
                                                <div class="targetgroup">Language<br>
                                                    <strong>{{$row->content_language}}</strong>
                                                </div>
                                                <div class="targetgroup">Gender<br>
                                                    <strong>{{$row->content_attracts}}</strong>
                                                </div>
                                            </div>
                                            <div class="targetgroup forTag">
                                                @if(isset($row->tags))
                                                @foreach($row->tags as $tags)
                                                <span>#{{$tags->tags}}</span>
                                                @endforeach
                                                @endif
                                            </div>
                                        </div>
                                        <div class="userDetails4">
                                            <span class="infoPrice">
                                                <?php
                                                $newLivetreamPrice = 0;
                                                $addNewLivetreamPrice = 0;

                                                $user = App\Models\User::find($row->i_user_id);

                                                $fieldName = $advertising.'_price_'.$row->media;
                                                if($user->advertisingMethodPrice != null)
                                                {
                                                    $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                }
                                                ?>
                                                  {{number_format($newLivetreamPrice,2)}} €
                                            </span>
                                            <div class="btn-group checkedNameBox" role="group" data-bs-toggle="buttons">
                                                <label class="checkboxAllCheck">
                                                    <input id="selectUser1" type="checkbox" name="seclectCard" class="Checkall">
                                                    <span class="span{{$row->i_id}}" ><span class="selcc">Select</span></span>
                                                </label>
                                                <span class="handelpletform{{$row->i_id}}"><div class="removeButton"><label class="checkboxAllCheck"><a name="" id="{{$row->i_id}}" class="btn btn-danger remove" href="javascript:void(0)" role="button">Remove</a></label></div></span>
                                            </div>
                                        </div>
                                        <input type="hidden" name="" value="{{$newLivetreamPrice}}" class="price{{$row->i_id}}">
                                        <input type="hidden" name="" value="{{$row->followers}}" class="followers{{$row->i_id}}">
                                    </div>
                                @endif
                            @endforeach
                        @endif
                    </div>
                    @if(isset($totalCount))
                    <div class="influncerUpdate"  @if($totalCount!=0) style="display:block;" @else style="display:none;"   @endif  >
                        <div class="newInfluncer">
                            Summary
                        </div>
                        <div><span class="allk">Total Influencers</span><span class="totalCount">{{ isset($totalCount)?$totalCount:0 }}</span> Influencers</div>
                        <div><span class="allk">Total Reach</span><span class="totalFollowers">{{ isset($totalFollowers)?$totalFollowers:0 }}</span> Followers</div>
                        <div><span class="allk">Total Cost</span>  <span class="totalPrice">{{ isset($totalPrice)?number_format($totalPrice,2):0 }} €</span></div>

                        <a  class=""><input name="" id="" class="btn btn-primary reqBtn requestDialogue" type="button" value="Request Now"   ></a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        <!-- Selected users //-->
    </div>
    @endif
    <div class="row markeplaceUserRequest">
         @include('front-user.pages.marketplace-request')
    </div>
