
@if(isset($tasks) )  
  <div class="task-div-outer d-flex justify-content-center">
    <div class="form-group">
      <div class="top-title task-title text-center"><img src="{{ asset('/assets/front-end/images/icons/icon-required-tasks.svg') }}" class="" alt=""> Required Tasks</div>
      <div class="custom-task-list checked-list">
        @foreach($tasks as $task)
          @if($task->task_type == 'required')
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="task[]" value="{{$task->id}}" id="task-id{{$task->id}}" data-parsley-errors-container="#error-task" data-parsley-error-message="Please confirm that all tasks have been completed." data-parsley-checkmin="1" checked readonly>
              <label class="form-check-label" for="task-id{{$task->id}}">
                {{$task->task}}
              </label>
            </div>
            @endif
          @endforeach 
      </div>
    </div>
    <div class="form-group">
      <div class="top-title task-title text-center"><img src="{{ asset('/assets/front-end/images/icons/icon-optional-tasks.svg') }}" class="" alt=""> Additional tasks</div>
      <div class="custom-task-list">
        @foreach($tasks as $task)
          @if($task->task_type == 'additional')

            <div class="form-check">
              <input class="form-check-input task_additional" type="checkbox" name="task_additional[]" value="{{$task->id}}" id="task-id{{$task->id}}" data-parsley-errors-container="#error-task" data-parsley-error-message="Please confirm that all tasks have been completed." data-parsley-checkmin="1"  >
              <label class="form-check-label" for="task-id{{$task->id}}">
                {{$task->task}}
              </label>
            </div>
            @endif 
          @endforeach 
          <span id="error-task"></span>
      </div>
    </div> 
  </div>
      
  @endif