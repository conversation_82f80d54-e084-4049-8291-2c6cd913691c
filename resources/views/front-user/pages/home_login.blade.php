@if( Auth::check())	
	@extends('front-user.layouts.master_user_dashboard')

	@section('content')

	<div class="page_tab">
		<main class="all-content">
			<div class="container">
				<div class="col-12">
					@if( Auth::check())
						@if(  Auth::user()->user_type == 'influencer') 
		
							@if($reqCount > 0) 
							<div class="pagesections">
								<div class="home-text font-bold">Attention, {{Auth::user()->first_name}} {{Auth::user()->last_name}}! You have <span class="orange-color">{{$reqCount}}</span>  pending requests awaiting your review. Take a moment to click the button below and check these inquiries. Don't miss out on potential opportunities review and accept them now!</div>
								<a  href="{{url('/create-campaign')}}" class="home-button">Create campaign</a>
							</div>
							<div class="pagesections">
								<div class="home-text font-bold">You currently have <span class="orange-color">{{$postCount}}</span>  active compaigns that require your post submissions. It's time to bring your A-game, get those creative juices flowing, and deliver exceptional content. Let's make each compaign shine. and leave a lasting impact on your audience!
								</div>
								<a  href="{{url('/open-campaigns')}}" class="home-button">Check open campaigns</a>
							</div>
							@else

							<div class="pagesections">
									<div class="home-text font-bold">Hey, {{Auth::user()->first_name}} {{Auth::user()->last_name}}! Currently, you don't have any pending requests. Stay on top of your game and keep an eye out for upcoming opportunities. We'll notify you via email or through our notifications system as soon as new requests come in. Stay proactive and be ready to seize the next exciting collaboration!</div>
							</div>
							@endif

						@else
							<div class="pagesections">
								<div class="home-text font-bold">Hello {{Auth::user()->first_name}} {{Auth::user()->last_name}},</div>
								<div class="home-text">cant wait to book influencers effectively without any communication, within a few minutes? </div>
								<a  href="{{url('/create-campaign')}}" class="home-button">Create campaign</a>
							</div>
							<div class="pagesections">
								<div class="home-text font-bold">Attention, valued Brands! You have <span class="orange-color">{{$pendingSubmit}}</span> open campaigns ready to take flight. It's time to unleash your brand's potential and make magic happen with these exciting opportunities!
								</div>
								<a  href="{{url('/open-campaigns')}}" class="home-button">Check open campaigns</a>
							</div>
							<div class="pagesections">
								<div class="home-text font-bold">Exciting news! <span class="orange-color">{{$influencerDataActive}}</span> influencers have submitted their posts for your review. Dive in and discover the incredible content they've created just for you. It's time to review!
								</div>
								<a href="{{url('/active-campaigns')}}" class="home-button">Review</a>
							</div>
					@endif
							@endif
					<div class="pagesections">
						<div class="faqHeading">FAQ</div>
						<div class="accordion" id="accordionExample">
							@php $i=1; @endphp
							@foreach($faqs as $faq)
							<div class="accordion-item">
								<h2 class="accordion-header" id="heading{{$faq->id}}">
								<button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{$faq->id}}"  aria-controls="collapse{{$faq->id}}" @if($i == 1) aria-expanded="true" @endif >
									{{$faq->question}}
								</button>
								</h2>
								<div id="collapse{{$faq->id}}" class="accordion-collapse collapse  @if($i == 1) show @endif " aria-labelledby="heading{{$faq->id}}" data-bs-parent="#accordionExample">
								<div class="accordion-body">
									{{$faq->answer}}
								</div>
								</div>
							</div>
							@php $i++; @endphp
							@endforeach
						</div>
					</div>
				</div>
			</div>
		</main>
	</div>
	@endsection
@else
	@extends('front-user.layouts.master_user')

	@section('content')
	<main class="all-content">
		<section class="home-banner wow fadeInUp">		
			<div class="container">
				<div class="row align-items-center">
					<div class="col-lg-6 col-md-6 col-12">
						<div class="image" data-aos="fade-down" data-aos-delay="500" data-aos-duration="1200">
							<img src="{{ asset('/') }}/assets/front-end/images/3918927-ai.png" alt="">
						</div>
					</div>
					<div class="col-lg-6 col-md-6 col-12">
						<div class="banner-title">
							<h3 class="title" data-aos="zoom-in" data-aos-delay="600" data-aos-duration="1200">Lorem ipsum dolor sit amet consectetur</h3>
							<p data-aos="zoom-in" data-aos-duration="1200" data-aos-delay="900">
								Lorem ipsum dolor sit amet consectetur adipisicing elit. Ab tenetur, dignissimos maxime natus corporis incidunt Ab tenetur, dignissimos maxime natus corporis
							</p> 
						</div>
					</div>
				</div> 
			</div>
		</section>


		<div class="servicesSection">
			<div class="container">
				<div class="row">
					<div class="col-lg-4 col-sm-6">
						<div class="serOtr d-flex" data-aos="fade-up"
		data-aos-anchor-placement="bottom-bottom" data-aos-delay="00" data-aos-duration="1200">
							<span class="serIcon"><i class="fa-solid fa-mobile"></i></span>
							<span class="serName">Lorem ipsum dolor</span>
						</div>
					</div>
					<div class="col-lg-4 col-sm-6">
						<div class="serOtr d-flex" data-aos="fade-up"
		data-aos-anchor-placement="bottom-bottom" data-aos-delay="400" data-aos-duration="1200">
							<span class="serIcon"><i class="fa-solid fa-circle-nodes"></i></span>
							<span class="serName">Lorem ipsum dolor</span>
						</div>
					</div>
					<div class="col-lg-4 col-sm-6">
						<div class="serOtr d-flex" data-aos="fade-up"
		data-aos-anchor-placement="bottom-bottom" data-aos-delay="800" data-aos-duration="1200">
							<span class="serIcon"><i class="fa-solid fa-people-group"></i></span>
							<span class="serName">Lorem ipsum dolor</span>
						</div>
					</div>
				</div>
			</div>
		</div>


		<div class="aboutSection">
			<div class="container">
				<div class="row align-items-center">
					<div class="col-md-6" data-aos="fade-right" data-aos-delay="400" data-aos-duration="1200" >
						<div class="subTitle">Lorem ipsum dolor sit amet consectetur adipisicing elit.</div>
						<div class="sectionTitle">About Us</div>
						<div class="sectiontext">
						Lorem ipsum dolor sit amet consectetur adipisicing elit. Ab tenetur, dignissimos maxime natus corporis incidunt Ab tenetur, dignissimos maxime natus corporis Lorem ipsum dolor sit amet consectetur adipisicing elit. Ab tenetur, dignissimos maxime natus corporis incidunt Ab tenetur, dignissimos maxime natus corporis Lorem ipsum dolor sit amet consectetur adipisicing elit. Ab tenetur, dignissimos maxime natus corporis incidunt Ab tenetur, dignissimos maxime natus corporis
						</div>
					</div>
					<div class="col-md-6" data-aos="fade-left" data-aos-delay="800" data-aos-duration="1200" data-aos-anchor-placement="top-center">
						<div class="sectionImage">
							<img src="{{ asset('/') }}/assets/front-end/images/abt-Image.jpg" alt="">
						</div>
					</div>
				</div>
			</div>
		</div>


		<div class="influncers">
			<div class="container">
				<div class="text-center">
					<div class="subTitle">Lorem ipsum dolor sit amet consectetur adipisicing elit.</div>
					<div class="sectionTitle">Top Influencer</div>
				</div>
				<!-- <div class="row">
					<div class="col-md-3 col-sm-6">
						<div class="card-wrapper">
							<img src="{{ asset('/') }}/assets/front-end/images/team1.jpg" alt="Mobirise">
							<div class="card-box">
								<h4 class="card-title"><strong>James Ford</strong></h4>
								<h5 class="card-text"><strong>General Director</strong></h5>
							</div>
						</div>
					</div>
					<div class="col-md-3 col-sm-6">
						<div class="card-wrapper">
							<img src="{{ asset('/') }}/assets/front-end/images/team2.jpg" alt="Mobirise">
							<div class="card-box">
								<h4 class="card-title"><strong>James Ford</strong></h4>
								<h5 class="card-text"><strong>General Director</strong></h5>
							</div>
						</div>
					</div>
					<div class="col-md-3 col-sm-6">
						<div class="card-wrapper">
							<img src="{{ asset('/') }}/assets/front-end/images/team3.jpg" alt="Mobirise">
							<div class="card-box">
								<h4 class="card-title"><strong>James Ford</strong></h4>
								<h5 class="card-text"><strong>General Director</strong></h5>
							</div>
						</div>
					</div>
					<div class="col-md-3 col-sm-6">
						<div class="card-wrapper">
							<img src="{{ asset('/') }}/assets/front-end/images/team1.jpg" alt="Mobirise">
							<div class="card-box">
								<h4 class="card-title"><strong>James Ford</strong></h4>
								<h5 class="card-text"><strong>General Director</strong></h5>
							</div>
						</div>
					</div>
				</div> -->
				<div class="row">
					<div class="col-lg-3 col-sm-6 col-12">
						<div class="userDetails" data-aos="zoom-in" data-aos-delay="400" data-aos-duration="1200">
							<div class="userDetails1 d-flex">
								<span class="handelname">@akon</span>
								<span class="handelpletform">
									<img src="{{ asset('/') }}/assets/front-end/images/instagram.png" alt="">
								</span>
							</div>
							<div class="userDetails2">
								<div class="userDetailImage">
									<img src="{{ asset('/') }}/assets/front-end/images/team1.jpg" class="iconColored" alt="">
								</div>
								<div class="userDetailInfo">
									<span class="infoName">6,8M Follower</span>
									<span class="infoStar">
										<i class="fa-solid fa-star"></i>
										<i class="fa-solid fa-star"></i>
										<i class="fa-solid fa-star"></i>
										<i class="fa-regular fa-star"></i>
										<i class="fa-regular fa-star"></i>
									</span>
								</div>
							</div>
							<div class="userDetails3">
								<div class="targetHeading">Target groups</div>
								<div class="targetgroups d-flex flex-wrap">
									<div class="targetgroup"><strong>Age</strong></div>
									<div class="targetgroup"><strong>Language</strong></div>
									<div class="targetgroup"><strong>Gender</strong></div>
								</div>
								<div class="targetgroup"><strong>#Tags(Up to 3)</strong></div>
							</div>
							<div class="userDetails4">
								<span class="infoPrice">
									€ 6000
								</span>
								<div class="btn-group" role="group" data-bs-toggle="buttons">
									<a href="#" class="sselectBtn">Select</a>
								</div>
							</div>
						</div>
					</div>
					<div class="col-lg-3 col-sm-6 col-12">
						<div class="userDetails" data-aos="zoom-in" data-aos-delay="700" data-aos-duration="1200">
							<div class="userDetails1 d-flex">
								<span class="handelname">@akon</span>
								<span class="handelpletform">
									<img src="{{ asset('/') }}/assets/front-end/images/instagram.png" alt="">
								</span>
							</div>
							<div class="userDetails2">
								<div class="userDetailImage">
									<img src="{{ asset('/') }}/assets/front-end/images/team2.jpg" class="iconColored" alt="">
								</div>
								<div class="userDetailInfo">
									<span class="infoName">6,8M Follower</span>
									<span class="infoStar">
										<i class="fa-solid fa-star"></i>
										<i class="fa-solid fa-star"></i>
										<i class="fa-solid fa-star"></i>
										<i class="fa-regular fa-star"></i>
										<i class="fa-regular fa-star"></i>
									</span>
								</div>
							</div>
							<div class="userDetails3">
								<div class="targetHeading">Target groups</div>
								<div class="targetgroups d-flex flex-wrap">
									<div class="targetgroup"><strong>Age</strong></div>
									<div class="targetgroup"><strong>Language</strong></div>
									<div class="targetgroup"><strong>Gender</strong></div>
								</div>
								<div class="targetgroup"><strong>#Tags(Up to 3)</strong></div>
							</div>
							<div class="userDetails4">
								<span class="infoPrice">
									€ 6000
								</span>
								<div class="btn-group" role="group" data-bs-toggle="buttons">
									<a href="#" class="sselectBtn">Select</a>
								</div>
							</div>
						</div>
					</div>
					<div class="col-lg-3 col-sm-6 col-12">
						<div class="userDetails" data-aos="zoom-in" data-aos-delay="1000" data-aos-duration="1200">
							<div class="userDetails1 d-flex">
								<span class="handelname">@akon</span>
								<span class="handelpletform">
									<img src="{{ asset('/') }}/assets/front-end/images/instagram.png" alt="">
								</span>
							</div>
							<div class="userDetails2">
								<div class="userDetailImage">
									<img src="{{ asset('/') }}/assets/front-end/images/team3.jpg" class="iconColored" alt="">
								</div>
								<div class="userDetailInfo">
									<span class="infoName">6,8M Follower</span>
									<span class="infoStar">
										<i class="fa-solid fa-star"></i>
										<i class="fa-solid fa-star"></i>
										<i class="fa-solid fa-star"></i>
										<i class="fa-regular fa-star"></i>
										<i class="fa-regular fa-star"></i>
									</span>
								</div>
							</div>
							<div class="userDetails3">
								<div class="targetHeading">Target groups</div>
								<div class="targetgroups d-flex flex-wrap">
									<div class="targetgroup"><strong>Age</strong></div>
									<div class="targetgroup"><strong>Language</strong></div>
									<div class="targetgroup"><strong>Gender</strong></div>
								</div>
								<div class="targetgroup"><strong>#Tags(Up to 3)</strong></div>
							</div>
							<div class="userDetails4">
								<span class="infoPrice">
									€ 6000
								</span>
								<div class="btn-group" role="group" data-bs-toggle="buttons">
									<a href="#" class="sselectBtn">Select</a>
								</div>
							</div>
						</div>
					</div>
					<div class="col-lg-3 col-sm-6 col-12">
						<div class="userDetails" data-aos="zoom-in" data-aos-delay="1300" data-aos-duration="1200">
							<div class="userDetails1 d-flex">
								<span class="handelname">@akon</span>
								<span class="handelpletform">
									<img src="{{ asset('/') }}/assets/front-end/images/instagram.png" alt="">
								</span>
							</div>
							<div class="userDetails2">
								<div class="userDetailImage">
									<img src="{{ asset('/') }}/assets/front-end/images/userImage.jpg" class="iconColored" alt="">
								</div>
								<div class="userDetailInfo">
									<span class="infoName">6,8M Follower</span>
									<span class="infoStar">
										<i class="fa-solid fa-star"></i>
										<i class="fa-solid fa-star"></i>
										<i class="fa-solid fa-star"></i>
										<i class="fa-regular fa-star"></i>
										<i class="fa-regular fa-star"></i>
									</span>
								</div>
							</div>
							<div class="userDetails3">
								<div class="targetHeading">Target groups</div>
								<div class="targetgroups d-flex flex-wrap">
									<div class="targetgroup"><strong>Age</strong></div>
									<div class="targetgroup"><strong>Language</strong></div>
									<div class="targetgroup"><strong>Gender</strong></div>
								</div>
								<div class="targetgroup"><strong>#Tags(Up to 3)</strong></div>
							</div>
							<div class="userDetails4">
								<span class="infoPrice">
									€ 6000
								</span>
								<div class="btn-group" role="group" data-bs-toggle="buttons">
									<a href="#" class="sselectBtn">Select</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>


		<div class="counterSection">
			<div class="container position-relative">
				<div class="text-center">
					<div class="subTitle">Lorem ipsum dolor sit amet consectetur adipisicing elit.</div>
					<div class="sectionTitle">Our Work</div>
					<div class="sectiontext">
						Lorem ipsum dolor sit amet consectetur adipisicing elit. Ab tenetur, dignissimos maxime natus corporis incidunt Ab tenetur, dignissimos maxime natus corporis Lorem ipsum dolor sit amet consectetur adipisicing elit. Ab tenetur, dignissimos maxime natus corporis incidunt Ab tenetur, dignissimos maxime natus corporis Lorem ipsum dolor sit amet consectetur adipisicing elit. Ab tenetur, dignissimos maxime natus corporis incidunt Ab tenetur, dignissimos maxime natus corporis
					</div>
				</div>
				<div class="conterOuter" id="counter" data-aos="fade-up"
		data-aos-anchor-placement="bottom-bottom" data-aos-delay="400" data-aos-duration="1200">
					<div class="conterAll">
						<span class="ttlCntr">Users</span>
						<span class="numCntr">
							<div>
								<div class="counter-value" data-count="2300">0</div>
							</div>
						</span>
					</div>
					<div class="conterAll">
						<span class="ttlCntr">Influencers</span>
						<span class="numCntr">
							<div>
								<div class="counter-value" data-count="4000">0</div>
							</div>
						</span>
					</div>
					<div class="conterAll">
						<span class="ttlCntr">Countries</span>
						<span class="numCntr">
							<div>
								<div class="counter-value" data-count="1500">0</div>
							</div>
						</span>
					</div>
					<div class="conterAll">
						<span class="ttlCntr">Per Day Users</span>
						<span class="numCntr">
							<div>
								<div class="counter-value" data-count="1200">0</div>
							</div>
						</span>
					</div>
				</div>

			</div>
		</div>


		<div class="influncers">
			<div class="container">
				<div class="text-center">
					<div class="subTitle">Lorem ipsum dolor sit amet consectetur adipisicing elit.</div>
					<div class="sectionTitle">Latest Blogs</div>
				</div>
				<div class="row">
				@foreach($blogs as $blog)
					<div class="col-lg-4 col-md-6 col-12">
						<div class="blogBox">
							<div class="blogBoxImg">
								<img src="{{asset('storage/' . $blog->image}}" alt="">
							</div>
							<div class="blogBoxContent">
								<div class="blogBoxBottom">
									<div class="blogby">By {{$blog->author?$blog->author:'Admin'}}</div>&nbsp;&nbsp;|&nbsp;&nbsp; 
									<div class="blogdate">{{date('F d, Y',strtotime($blog->created_at))}}</div>
								</div>
								<!-- <div class="blogview"><a href="{{url('blog-detail').'/'.$blog->slug}}">Read More</a></div> -->
								<h3><a href="{{url('blog-detail').'/'.$blog->slug}}">{{$blog->title}}</a></h3>
								<div class="blogDescription">{!! $blog->description !!}</div>
								<div class="blogBoxBottom">
									<div class="blogview"><a href="{{url('blog-detail').'/'.$blog->slug}}">Read More</a></div>
								</div>
							</div>
						</div>
					</div>
				@endforeach 
				</div>
			</div>
		</div>
	</main>
	<main class="all-content">
		<div class="container">
			<div class="col-12">
				@if( Auth::check())
					@if(  Auth::user()->user_type == 'influencer') 
	
						@if($reqCount > 0) 
						<div class="pagesections">
							<div class="home-text font-bold">Attention, {{Auth::user()->first_name}} {{Auth::user()->last_name}}! You have <span class="orange-color">{{$reqCount}}</span>  pending requests awaiting your review. Take a moment to click the button below and check these inquiries. Don't miss out on potential opportunities review and accept them now!</div>
							<a  href="{{url('/create-campaign')}}" class="home-button">Create campaign</a>
						</div>
						<div class="pagesections">
							<div class="home-text font-bold">You currently have <span class="orange-color">{{$postCount}}</span>  active compaigns that require your post submissions. It's time to bring your A-game, get those creative juices flowing, and deliver exceptional content. Let's make each compaign shine. and leave a lasting impact on your audience!
							</div>
							<a  href="{{url('/open-campaigns')}}" class="home-button">Check open campaigns</a>
						</div>
						@else

						<div class="pagesections">
								<div class="home-text font-bold">Hey, {{Auth::user()->first_name}} {{Auth::user()->last_name}}! Currently, you don't have any pending requests. Stay on top of your game and keep an eye out for upcoming opportunities. We'll notify you via email or through our notifications system as soon as new requests come in. Stay proactive and be ready to seize the next exciting collaboration!</div>
						</div>
						@endif

					@else
						<div class="pagesections">
							<div class="home-text font-bold">Hello {{Auth::user()->first_name}} {{Auth::user()->last_name}},</div>
							<div class="home-text">cant wait to book influencers effectively without any communication, within a few minutes? </div>
							<a  href="{{url('/create-campaign')}}" class="home-button">Create campaign</a>
						</div>
						<div class="pagesections">
							<div class="home-text font-bold">Attention, valued Brands! You have <span class="orange-color">{{$pendingSubmit}}</span> open campaigns ready to take flight. It's time to unleash your brand's potential and make magic happen with these exciting opportunities!
							</div>
							<a  href="{{url('/open-campaigns')}}" class="home-button">Check open campaigns</a>
						</div>
						<div class="pagesections">
							<div class="home-text font-bold">Exciting news! <span class="orange-color">{{$influencerDataActive}}</span> influencers have submitted their posts for your review. Dive in and discover the incredible content they've created just for you. It's time to review!
							</div>
							<a href="{{url('/active-campaigns')}}" class="home-button">Review</a>
						</div>
				@endif
						@endif
				<div class="pagesections">
					<div class="faqHeading">FAQ</div>
					<div class="accordion" id="accordionExample">
						@php $i=1; @endphp
						@foreach($faqs as $faq)
						<div class="accordion-item">
							<h2 class="accordion-header" id="heading{{$faq->id}}">
							<button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{$faq->id}}"  aria-controls="collapse{{$faq->id}}" @if($i == 1) aria-expanded="true" @endif >
								{{$faq->question}}
							</button>
							</h2>
							<div id="collapse{{$faq->id}}" class="accordion-collapse collapse  @if($i == 1) show @endif " aria-labelledby="heading{{$faq->id}}" data-bs-parent="#accordionExample">
							<div class="accordion-body">
								{{$faq->answer}}
							</div>
							</div>
						</div>
						@php $i++; @endphp
						@endforeach
					</div>
				</div>
			</div>
		</div>
	</main>
	@endsection
@endif




@section('script_links')

@endsection

@section('script_codes')
<script>
	var a = 0;
	$(window).scroll(function() {

	var oTop = $('#counter').offset().top - window.innerHeight;
	if (a == 0 && $(window).scrollTop() > oTop) {
		$('.counter-value').each(function() {
		var $this = $(this),
			countTo = $this.attr('data-count');
		$({
			countNum: $this.text()
		}).animate({
			countNum: countTo
			},

			{

			duration: 2000,
			easing: 'swing',
			step: function() {
				$this.text(Math.floor(this.countNum));
			},
			complete: function() {
				$this.text(this.countNum);
			}

			});
		});
		a = 1;
	}
})

</script>
@endsection