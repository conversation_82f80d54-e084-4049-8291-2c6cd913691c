@extends('front-user.layouts.master_user_dashboard')

@section('content')
<style>
     .lets_go_btn {
       margin-top: -50px;
     }
    @media only screen and (max-width: 600px) {
  .lets_go_btn {
   margin-left: 16px !important;
   margin-top: 53px !important;
  }
}
@media (max-width: 768px) {
    #usr_online img {
        width: 50%;
    }
    #usr_offline img {
        width: 50%;
    }
    .pt-4, .pt-5 {
        padding-top: 20px;
    }
}
</style>

@php

    $Influencer = App\Models\InfluencerDetail::where('user_id',Auth::id())->first();

    $tot_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id',@$Influencer->id)
                    ->where('finish',NULL)->where('refund_reason',NULL)
                    ->where(function ($query) {
                        $query->where('review', '!=', 1) ;
                    })
                    ->count();
    $open_count =   App\Models\InfluencerRequestDetail::join('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )
                        ->select('influencer_request_details.*')
                        ->where('influencer_request_details.influencer_detail_id',@$Influencer->id)
                        ->where('influencer_request_details.finish',NULL)
                        ->where('influencer_request_details.refund_reason',NULL)
                    ->where(function ($query) {
                        $query->where('influencer_request_details.review', '=', NULL)
                                ->orWhere('influencer_request_details.review', '=', 0);
                    })
                        ->count();


    $req_count =$tot_count ;


    $myCampaignList =    App\Models\InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )
                ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )
                ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )
                    ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept','influencer_request_accepts.id as influencer_request_accept_id' ,'influencer_request_accepts.created_at as accept_time')
                ->where('influencer_details.user_id',Auth::id())
                ->orderBy('influencer_request_details.id','desc')->get();

    $postCount = 0 ;
    $postCountReq = 0 ;
    foreach($myCampaignList as $row){
        if($row->request == 1 &&  $row->invoice_id!='' && $row->review !='1' && $row->review !='0'  &&   $row->refund_reason =='' ){
                $postCountReq++;
                if($row->invoice_id!=''){
                    $postCount++;
                }
        }
    }

    $reqCountList = App\Models\InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )
                ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )
                ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )
                    ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept','influencer_request_accepts.id as influencer_request_accept_id' )
                ->where('influencer_details.user_id',Auth::id())
                ->orderBy('influencer_request_details.id','desc')->get() ;

    $campaignRequestTime = App\Models\CampaignRequestTime::first();
    $reqCount = 0;
    $openCountReq = 0;
    foreach($reqCountList as $row) {
        if ($row->invoice_id=='' && $row->review != '2' && $row->refund_reason == '') {
            $time = (isset($row->request_time_accept) && $row->request_time_accept == 1)?$row->request_time+$row->time:$row->time;
            $created_date =  date('Y-m-d H:i:s',strtotime($row->created_at));
            $updated_date =  date('Y-m-d H:i:s',strtotime($row->updated_at));
            $campaignDate= date('Y-m-d H:i:s', strtotime($created_date. ' + '.$campaignRequestTime->request_time.' days'));
            $date = date('Y-m-d H:i:s');
            $seconds = strtotime($campaignDate) - strtotime($date);

            $days = floor($seconds / 86400);
            if($date <= $campaignDate ){
                    $openCountReq++;
                    $reqCount++;
            }
        }
    }
    $tot = $reqCount;
    $req_count = $postCountReq+$openCountReq;
@endphp

<section>
    <div class="step_form_custom">
        <div class="steps-section">
            <div class="step_form_link" id="step_form_link">
                <div class="ser_op" id="tablink0">
                    <div class="steps-icon">
                        <img src="{{ asset('/assets/front-end/images/icons/profile_info.svg') }}"
                            class="step-icon-check" alt="">
                        <span class="mobile-text">1</span>
                    </div>
                    <span class="numberTab">1</span> <span class="tabText">Profile Information</span>
                </div>

                <div class="ser_op disabled" id="tablink1">
                    <div class="steps-icon">
                        <img src="{{ asset('/assets/front-end/images/icons/social_media.svg') }}"
                            class="step-icon-check" alt="">
                        <span class="mobile-text">2</span>
                    </div>
                    <span class="numberTab">2</span> <span class="tabText">Social Media</span>
                </div>
                <div class="ser_op disabled" id="tablink2">
                    <div class="steps-icon">
                        <img src="{{ asset('/assets/front-end/images/icons/general_info.svg') }}"
                            class="step-icon-check" alt="">
                        <span class="mobile-text">3</span>
                    </div>
                    <span class="numberTab">3</span> <span class="tabText">General Information</span>
                </div>
                <div class="ser_op disabled" id="tablink3">
                    <div class="steps-icon">
                        <img src="{{ asset('/assets/front-end/images/icons/oi_target.svg') }}"
                            class="step-icon-check" alt="">
                        <span class="mobile-text">4</span>
                    </div>
                    <span class="numberTab">4</span> <span class="tabText">Select Target Group</span>
                </div>
                <div class="ser_op disabled" id="tablink4">
                    <div class="steps-icon">
                        <img src="{{ asset('/assets/front-end/images/icons/campaign_type.svg') }}"
                            class="step-icon-check" alt="">
                        <span class="mobile-text">5</span>
                    </div>
                    <span class="numberTab">5</span> <span class="tabText">Campaign Types</span>
                </div>
                <div class="ser_op disabled" id="tablink5">
                    <div class="steps-icon" @if(!(Auth::user()->status==1 && $req_count < 5)) style="border: 1px solid #CD2727;" @endif>
                        @if(Auth::user()->status==1 && $req_count < 5)
                            <img src="{{ asset('/assets/front-end/images/icons/go_live.svg') }}" class="step-icon-check" alt="">
                        @else
                            <img src="{{ asset('/assets/front-end/images/icons/market_place_offline.svg') }}" class="step-icon-check" alt="">
                        @endif
                        <span class="mobile-text">6</span>
                    </div>
                    <span class="numberTab">6</span> <span class="tabText">Go Live</span>
                </div>
            </div>
        </div>
        <input type="hidden" value="" id="formId">
        <div class="step_form">
            <div class="steps_con" id="collection0">
                <div class="mobile-step-detail">
                    <div class="steps-cont">
                        STEP 01
                    </div>
                    <div class="steps-which">
                        Profile Information
                    </div>
                </div>
                <div id="stripeError"></div>
                <form action="{{ url('/my-profile') }}" method="post" data-parsley-validate id="draft0">
                    @csrf
                    <div class="row">
                        <div class="col-12 text-center">

                            @if (!$stripeAccount)
                                <a class="profile-connect-btn"
                                    href="https://connect.stripe.com/oauth/authorize?response_type=code&client_id={{ config('settings.env.STRIPE_CLIENT_ID') }}&redirect_uri={{url('connect-stripe-account')}}&scope=read_write&stripe_user[email]={{ Auth::user()->email }}">Connect with Stripe</a>
                                    <input type="hidden" id="stripe_status" value="not_connected">
                            @else
                            @php
                                $social_connect_media = App\Models\SocialConnect::where('user_id',Auth::id())->first();
                            @endphp
                            <div class="d-flex flex-column justify-center align-items-center">
                                <input type="hidden" id="stripe_status" value="connected">
                            </div>
                            @endif
                        </div>
                        @csrf

                        <div class="col-12"></div>
                        <div class="col-lg-6 col-12">
                            <div class="form-group" @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">First Name <span class="color-red">*</span></label>
                                <div class="floating-label smalSpace">
                                    <input type="text" class="form-control floating-input profile-input"
                                        id="rqrIn1" keypress="doFocus(1)" placeholder="First Name" name="first_name"
                                        required="" data-parsley-required-message="Please enter first name."
                                        value="{{ Auth::user()->first_name }}">
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-12">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">Last Name <span class="color-red">*</span></label>
                                <div class="floating-label smalSpace">
                                    <input type="text" class="form-control floating-input profile-input"
                                        id="rqrIn2" keypress="doFocus(2)" placeholder="Last Name" name="last_name"
                                        required="" data-parsley-required-message="Please enter last name."
                                        value="{{ Auth::user()->last_name }}">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-12">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">Email Id</label>
                                <div class="floating-label smalSpace">
                                    <input type="email" class="form-control floating-input profile-input"
                                        placeholder="Email Id" name="email" readonly=""
                                        value="{{ Auth::user()->email }}">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-12">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">Phone Number</label>
                                <div class="floating-label smalSpace">
                                    <input type="number"
                                        class="form-control floating-input profile-input optional-input"
                                        placeholder="Phone Number" id="oprIn1" name="phone"
                                        data-parsley-required-message="Please enter phone." data-parsley-type="digits"
                                        data-parsley-type-message="Please enter a valid phone number."
                                        data-parsley-maxlength="20"
                                        data-parsley-maxlength-message="Max length 20 numbers."
                                        value="{{ Auth::user()->phone }}">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-12">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">Company Name @if (Auth::user()->user_type == 'customer')
                                        <span class="color-red">*</span>
                                    @endif </label>
                                <div class="floating-label smalSpace">
                                    <input type="text"
                                        class="form-control floating-input profile-input optional-input"
                                        placeholder="Company Name" id="oprIn2" name="company_name"
                                        value="{{ Auth::user()->company_name }}"
                                        @if (Auth::user()->user_type == 'customer') required @endif
                                        data-parsley-required-message="Please enter Company Name.">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-12">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">Tax ID <span class="color-red">*</span></label>
                                <div class="floating-label smalSpace">
                                    <input type="text"
                                        class="form-control floating-input profile-input optional-input"
                                        placeholder="Tax ID" id="oprIn5" name="vat_id"
                                        value="{{ Auth::user()->vat_id }}" required
                                        data-parsley-required-message="Please enter VAT or Tax ID."
                                        data-parsley-pattern="^(?:[A-Z]{2}\d+|\d+)$"
                                        data-parsley-length="[9, 11]"
                                        data-parsley-pattern-message="Enter a valid VAT ID (e.g., DE123456789) or Tax ID (***********).">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-12 reqr">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">Country <span class="color-red">*</span></label>
                                <div class="floating-label smalSpace reqr">

                                    <select class="select form-control floating-input profile-select" name="country"
                                        required="" id="rqrIn3country"
                                        data-parsley-required-message="Please enter Country."
                                        data-parsley-errors-container="#error-countery">
                                        <option value="">Select Country</option>
                                        @foreach ($countries as $country)
                                            <option @if (Auth::user()->country == $country->id) Selected @endif
                                                value="{{ $country->id }}">{{ $country->name }}</option>
                                        @endforeach
                                    </select>
                                    <span id="error-countery"></span>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-12 reqr">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">City <span class="color-red">*</span></label>
                                <div class="floating-label smalSpace reqr">
                                    <select class="select form-control floating-input profile-select" name="city"
                                        required="" id="rqrIn5"
                                        data-parsley-required-message="Please enter City."
                                        data-parsley-errors-container="#error-city"
                                        @if (Auth::user()->country == '') disabled @endif>
                                        <option value="">Select City</option>
                                        @foreach ($cities as $row)
                                        @if($row->name != '')
                                            <option value="{{ $row->id }}"
                                                @if (Auth::user()->city == $row->id) Selected @endif>{{ $row->name }}
                                            </option>
                                            @endif
                                        @endforeach
                                    </select>
                                    <span id="error-city"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-12">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">Street</label>
                                <div class="floating-label smalSpace">
                                    <input type="text"
                                        class="form-control floating-input profile-input optional-input"
                                        placeholder="Street" id="oprIn3" name="street"
                                        data-parsley-required-message="Please enter street."
                                        value="{{ Auth::user()->street }}">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-12">
                            <div class="form-group"  @if(!$stripeAccount) disabled @endif>
                                <label class="floatLabel">Post/Zip Code <span class="color-red">*</span></label>
                                <div class="floating-label smalSpace">
                                    <input type="text"
                                        class="form-control floating-input profile-input zip_code optional-input"
                                        name="zip_code" id="oprIn4" placeholder="Post/Zip Code"
                                        required="" data-parsley-required-message="Please enter post/zip code."
                                        value="{{ Auth::user()->zip_code }}">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="floatLabel">Are you a small business owner (according to <a href="https://www.gesetze-im-internet.de/ustg_1980/__19.html" target="_blank">§ 19 UStG</a>)? <span class="color-red">*</span></label>
                        <div class="floating-label smalSpace">
                            <div class="radio-group" style="display: flex; gap: 40px; padding-left: 30px; margin-top: 10px;">
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="is_small_business_owner" id="is_small_business_owner_yes" value="1"
                                    {{ Auth::user()->is_small_business_owner == 1 ? 'checked' : '' }}
                                    required
                                    data-parsley-required-message="Please select whether you are a small business owner."
                                    data-parsley-errors-container="#small-business-error"/>
                                    <label class="form-check-label" for="is_small_business_owner_yes" style="margin-left: 5px;">
                                        Yes
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="is_small_business_owner" id="is_small_business_owner_no" value="0"
                                    {{ Auth::user()->is_small_business_owner == 0 ? 'checked' : '' }}
                                    required
                                    data-parsley-required-message="Please select whether you are a small business owner."
                                    data-parsley-errors-container="#small-business-error"/>
                                    <label class="form-check-label" for="is_small_business_owner_no" style="margin-left: 5px;">
                                        No
                                    </label>
                                </div>
                            </div>
                            <span id="small-business-error" class="text-danger" style="padding-left: 30px; display: block; margin-top: 5px;"></span>
                        </div>
                    </div>
                    <div class="step-nevigationbutton">
                        <div class="nav-right next ms-auto">
                            <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" alt="Next">
                        </div>
                    </div>
                </form>
            </div>
            <div class="steps_con disabled" id="collection1">
                <div class="mobile-step-detail">
                    <div class="steps-cont">
                        STEP 02
                    </div>
                    <div class="steps-which">
                        Social media platforms
                    </div>
                </div>
                <div class="connectWith">
                    @include('front-user.pages.influencer-socialconnect')
                </div>
                <div class="step-nevigationbutton">
                    <div class="nav-left back me-2" id="Back_step0">
                        <img src="{{ asset('assets/front-end/images/icons/step-left.svg') }}" alt="">
                    </div>
                    <div class="nav-right next ms-auto">
                        <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" alt="">
                    </div>
                </div>
            </div>
            <div class="steps_con" id="collection2">
                <div class="mobile-step-detail">
                    <div class="steps-cont">
                        STEP 03
                    </div>
                    <div class="steps-which">
                        General information
                    </div>
                </div>
                <form name="general_information" action="{{ url('/save-influencer-form') }}" method="post" data-parsley-validate id="draft1">
                    @csrf
                    <div class="informationDiv generalInfo">
                        @include('front-user.pages.general-information')
                    </div>
                </form>
            </div>
            <div class="steps_con" id="collection3">
                <div class="mobile-step-detail">
                    <div class="steps-cont">
                        STEP 04
                    </div>
                    <div class="steps-which">
                        Select target group
                    </div>
                </div>
                <form action="{{ url('/save-influencer-form') }}" method="post" data-parsley-validate id="draft2">
                    @csrf
                    <div class="informationDiv targetGroup">
                        @include('front-user.pages.target-group')
                    </div>
                </form>
            </div>
            <div class="steps_con" id="collection4">
                <div class="mobile-step-detail">
                    <div class="steps-cont">
                        STEP 05
                    </div>
                    <div class="steps-which">
                        Campaign types
                    </div>
                </div>
                <form action="{{ url('/save-influencer-form') }}" method="post" data-parsley-validate data-parsley-excluded=[disabled] id="draft3">
                    @csrf
                    <input type="hidden" name="advertising_method" value="true">
                    <h3 align="center" style="color: #AD80FF !important;">Brands can request you only for the activated campaign types</h3>
                    <div class="informationDiv advertisingMethod1">
                        @include('front-user.pages.influencer-advertising-choose')
                    </div>
                </form>
            </div>
            <div class="steps_con" id="collection5">
                <div class="mobile-step-detail">
                    <div class="steps-cont">STEP 06</div>
                    <div class="steps-which">Go Live</div>
                </div>
                @if ($user->activate == '2')
                    <form action="{{ url('/save-influencer-form') }}" method="post" data-parsley-validate id="draft4">
                        @csrf
                        <input type="hidden" name="advertising_method_price" value="true">
                        <div class="informationDiv advertisingPriceMethod1">
                            @include('front-user.pages.influencer-pricing-choose')
                        </div>
                    </form>
                @else
                    <form action="{{ url('/save-influencer-form') }}" method="post" id="draft5">
                        @csrf
                        <div class="informationDiv">
                            <ul class="nav nav-tabs" id="myTab" role="tablist"></ul>
                            <div class="tab-content connectPublish" id="myTabContent">
                                @include('front-user.pages.influencer-publish')
                                <div class="offset-3 col-6 text-center lets_go_btn">
                                    <input type="hidden" name="publish" value="Publish">
                                    <input type="hidden" name="Publishonline" value="PublishOnline">
                                <button type="submit" class="button-ccg new-style  newpost float-none publish"
                                name="PublishOnline" value="PublishOnline">Let’s go online!</button>
                                </div>
                            </div>
                            <div class="step-nevigationbutton">
                                <div class="nav-left back me-2" id="Back_step4">
                                    <img src="{{ asset('assets/front-end/images/icons/step-left.svg') }}" alt="">
                                </div>
                                <div class="nav-right next ms-auto" >
                                    <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" alt="" style="background: #d5cbe9 !important;cursor:default;">
                                </div>
                            </div>
                        </div>
                    </form>
                @endif
            </div>

        </div>
    </div>
</section>

<!--social connect error popup Start -->
<div class="modal fade influncer" id="payouterrorpopup" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="payoutconfirmpopupLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="text-center errorBox">
                    <div class="text-center chkImage"><img src="{{ asset('/assets/front-end/images/icons/icon-no-data-image.png') }}" alt=""></div>
                    <div class="ortContentntr" id="errorText">Something went wrong!</div>
                    <button class="btnpor bg-error color-white mb-5" type="button" data-bs-dismiss="modal">Ok</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script_codes')
<script>
    // Helper function to safely get jQuery element values
    function safeVal(selector) {
        var element = $(selector);
        return element.length > 0 ? (element.val() || '') : '';
    }

    // Helper function to safely check if element exists and has value
    function hasValue(selector) {
        var val = safeVal(selector);
        return val && val.length > 0;
    }

    // top step count
    var mp_step = $('.step_form_link .ser_op')

    var iEra, thumbLi = $('.step_form_link .ser_op');
    if (mp_step && mp_step.length) {
        for (iEra = 0; iEra <= mp_step.length; iEra++) {
            thumbLi.eq(iEra).attr("id", 'tablink' + iEra);
            thumbLi.eq(iEra).find(".numberTab").text("Step 0" + (iEra + 1))
        }
    }

    // top steps
    var mp_step = $('.step_form .steps_con')

    var iEra, thumbLi = $('.step_form .steps_con');
    if (mp_step && mp_step.length) {
        for (iEra = 0; iEra <= mp_step.length; iEra++) {
            thumbLi.eq(iEra).attr("id", 'collection' + iEra);
        }
    }

    function saveDraft(count) {
        $('#draftcount').val(count);
        $(".publishButton").each(function() {
            var star1 = $(this);
            star1.val('Draft');
        });

        var hashtag1 = safeVal("#hashtag1");
        var hashtag2 = safeVal("#hashtag2");
        var hashtag3 = safeVal("#hashtag3");

        if (hashtag1 !== "" || hashtag2 !== "" || hashtag3 !== "") {
            if (hashtag1 === hashtag2 || hashtag1 === hashtag3 || hashtag2 === hashtag3) {
                $(".hashtagerror").show();
                return false;
            } else {
                $(".hashtagerror").hide();
            }
        }

        if (document.getElementById('user_status') && document.getElementById('user_status').checked) {
            $('#draftmode').modal('show');
        } else {
            $('#draft' + count).submit();
        }
    }

    if(safeVal("#stripe_status") == "not_connected") {
        $(".ser_op").addClass("disabled")
    }

    $(document).on("click", ".next", function(event) {
        if(safeVal("#stripe_status") == "connected") {
            const str = undefined;
                const checkaltr = $(this).closest(".steps_con").attr("data-content").split("collection");

                $(".content_language").each(function() {
                    var star1 = $(this);
                    star1.html(safeVal('select[name="content_language"]'));
                });
                $(".ages").each(function() {
                    var star2 = $(this);
                    star2.html(safeVal('select[name="ages"]'));
                });
                $(".content_attracts").each(function() {
                    var star3 = $(this);
                    star3.text(safeVal('input[name="content_attracts"]:checked'));
                });

                $('.infoPrice_youtube').html(safeVal('input[name="video_price_youtube"]'));
                $('.infoPrice_twitter').html(safeVal('input[name="video_price_twitter"]'));
                $('.infoPrice_twitch').html(safeVal('input[name="video_price_twitch"]'));
                $('.infoPrice_facebook').html(safeVal('input[name="video_price_facebook"]'));
                $('.infoPrice_instagram').html(safeVal('input[name="video_price_instagram"]'));

                $('.liveStream_youtube').html(safeVal('input[name="livestream_price_youtube"]'));
                $('.liveStream_twitter').html(safeVal('input[name="livestream_price_twitter"]'));
                $('.liveStream_twitch').html(safeVal('input[name="livestream_price_twitch"]'));
                $('.liveStream_facebook').html(safeVal('input[name="livestream_price_facebook"]'));
                $('.liveStream_instagram').html(safeVal('input[name="livestream_price_instagram"]'));

                $('.share_youtube').html(safeVal('input[name="share_price_youtube"]'));
                $('.share_twitter').html(safeVal('input[name="share_price_twitter"]'));
                $('.share_twitch').html(safeVal('input[name="share_price_twitch"]'));
                $('.share_facebook').html(safeVal('input[name="share_price_facebook"]'));
                $('.share_instagram').html(safeVal('input[name="share_price_instagram"]'));


                var target = $(this).closest('.steps_con').next('.steps_con').attr("data-content");
                var mainTarget = $(this).closest('.steps_con').attr("data-content");
                var slic = $(this).closest('.steps_con').next('.steps_con').attr("data-content").replace(/collection/, '');

                if (mainTarget != 'collection1') {
                    $(this).closest('.steps_con').find("form").each(function(){
                        $(this).closest('.steps_con').find("form").parsley().validate()
                        if ($(this).closest('.steps_con').find("form").parsley().isValid()) {
                            $('#selectcategory1').parsley().validate()
                            var formData = null;
                            if (mainTarget == 'collection0') {
                                formData = $('#draft0').serialize();
                                $.ajax({
                                    url: "{{ url('/my-profile') }}",
                                    type: "post",
                                    dataType: 'json',
                                    data: formData,
                                }).done(function(data) {
                                    // Profile saved successfully
                                })
                            }else if (mainTarget == 'collection2') {
                                var hashtag1 = safeVal("#hashtag1");
                                var hashtag2 = safeVal("#hashtag2");
                                var hashtag3 = safeVal("#hashtag3");

                                if (hashtag1 === hashtag2 || hashtag1 === hashtag3 || hashtag2 === hashtag3) {
                                    $(".hashtagerror").show();
                                    return false;
                                } else {
                                    $(".hashtagerror").hide();
                                }
                                formData = $('#draft1').serialize();
                            } else if (mainTarget == 'collection3') {
                                formData = $('#draft2').serialize();
                            } else if (mainTarget == 'collection4') {
                                formData = $('#draft3').serialize();

                            } else if (mainTarget == 'collection5') {
                                formData = $('#draft4').serialize();
                            }

                            if (formData != null) {
                                $.ajax({
                                    url: "{{ url('/save-influencer-form') }}",
                                    type: "post",
                                    dataType: 'json',
                                    data: formData,
                                }).done(function(data) {
                                    if (data.status == true) {
                                        $('.generalInfo').html(data.generalPage);
                                        $(".targetGroup").html(data.targetPage);
                                        $(".advertisingMethod1").html(data.advertisingPage);
                                        $(".advertisingPriceMethod1").html(data.advertisingPricePage);
                                        $(".connectPublish").html(data.publishPage);
                                        if (safeVal("select[name=sharecontent_media]") == "") {
                                            $("select[name=sharecontent_media]").closest(".select_media").find(
                                                "input[type=checkbox]").prop('checked', false);
                                            $("select[name=sharecontent_media]").prop('disabled', true);
                                            $("select[name=sharecontent_media]").closest(".select_media")
                                                .removeClass("checkedmedia")
                                        }
                                        if (safeVal("select[name=video_media]") == "") {
                                            $("select[name=video_media]").closest(".select_media").find(
                                                "input[type=checkbox]").prop('checked', false);
                                            $("select[name=video_media]").prop('disabled', true);
                                            $("select[name=video_media]").closest(".select_media").removeClass(
                                                "checkedmedia")
                                        }
                                        if (safeVal("select[name=livestream_media]") == "") {
                                            $("select[name=livestream_media]").closest(".select_media").find(
                                                "input[type=checkbox]").prop('checked', false);
                                            $("select[name=livestream_media]").prop('disabled', true);
                                            $("select[name=livestream_media]").closest(".select_media")
                                                .removeClass("checkedmedia")
                                        }

                                        $(".select").select2();
                                        $('div[data-title=' + target + ']').removeClass("disabled");
                                        var isValid = true;
                                        $('div[data-content=' + target + '] .form-control[required]').each(
                                        function() {
                                            if ($(this).val() === '') {
                                                isValid = false;
                                                event.stopPropagation();
                                            }
                                        });

                                        $("input.classtop").change(function() {
                                            var valueName = $(this).val();
                                            if ($(this).is(':checked')) {
                                                if ($(this).val() == "video_on_demand") {
                                                    $(".socialMediaSection input[data-id= video_on_demand]")
                                                        .attr("disabled", false);

                                                } else if ($(this).val() == "livestream") {
                                                    $(".socialMediaSection input[data-id=livestream]")
                                                        .attr("disabled", false);


                                                } else if ($(this).val() == "shareContent") {
                                                    $(".socialMediaSection input[data-id= shareContent]")
                                                        .attr("disabled", false);

                                                }
                                                $(this).closest(".select_media").addClass(
                                                    "checkedmedia");
                                            } else {
                                                $(".socialMediaSection input[data-id=" + valueName +
                                                    "]").attr("disabled", true).attr("checked",
                                                    false);
                                                $(this).closest(".select_media").removeClass(
                                                    "checkedmedia snapchat instagram twitter tiktok youtube facebook twitch"
                                                    );
                                            }

                                        });

                                        var media = $('input[name="media"]:checked').val();
                                        var type_count = @json($user);

                                        var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type = 'Boost me';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media,
                                                    type: type
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_boost").val(data.price);
                                                    $(".type_price_boost_text").text(data.price);
                                                    $(".type_price_boost_next").val(data.price_next);
                                                    $(".type_price_boost_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_boost_per").text(roundedPer);

                                                } else {
                                                    $(".type_price_boost").val('');
                                                    $(".type_price_boost_text").text('');
                                                    $(".type_price_boost_next").val('');
                                                    $(".type_price_boost_next_text").text('');
                                                    $(".price-current_boost_per").text('0');
                                                }
                                            });


                                        var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type1 = 'Reaction video';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media1,
                                                    type: type1
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_reaction").val(data.price);
                                                    $(".type_price_reaction_text").text(data.price);
                                                    $(".type_price_reaction_next").val(data.price_next);
                                                    $(".type_price_reaction_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_reaction_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_reaction").val('');
                                                    $(".type_price_reaction_text").text('');
                                                    $(".type_price_reaction_next").val('');
                                                    $(".type_price_reaction_next_text").text('');
                                                    $(".price-current_reaction_per").text('0');
                                                }
                                            });



                                        var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type2 = 'Survey';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media2,
                                                    type: type2
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_survey").val(data.price);
                                                    $(".type_price_survey_text").text(data.price);
                                                    $(".type_price_survey_next").val(data.price_next);
                                                    $(".type_price_survey_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_survey_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_survey").val('');
                                                    $(".type_price_survey_text").text('');
                                                    $(".type_price_survey_next").val('');
                                                    $(".type_price_survey_next_text").text('');
                                                    $(".price-current_survey_per").text('0');
                                                }
                                            });


                                        $(".campaign-type-content input[type=checkbox]").each(function() {
                                            var media = $(".campaign-type-content input").attr("data-media");
                                            if ($(this).is(':checked')) {
                                                var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                                var type = 'Boost me';
                                                $.ajax({
                                                        url: "{{ url('get-admin-pricing') }}",
                                                        data: {
                                                            media: media,
                                                            type: type
                                                        }
                                                    })
                                                    .done(function(data) {
                                                        if (data.price != null) {
                                                            $(".type_price_boost").val(data.price);
                                                            $(".type_price_boost_text").text(data.price);
                                                            $(".type_price_boost_next").val(data.price_next);
                                                            $(".type_price_boost_next_text").text(data.price_next);

                                                            var per = ((data.price_next / data.price) * 100) - 100;
                                                            const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                            $(".price-current_boost_per").text(roundedPer);
                                                        } else {
                                                            $(".type_price_boost").val('');
                                                            $(".type_price_boost_text").text('');
                                                            $(".type_price_boost_next").val('');
                                                            $(".type_price_boost_next_text").text(
                                                                '');
                                                            $(".price-current_boost_per").text('0');
                                                        }
                                                    });


                                                var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                                var type1 = 'Reaction video';
                                                $.ajax({
                                                    url: "{{ url('get-admin-pricing') }}",
                                                    data: {
                                                        media: media1,
                                                        type: type1
                                                    }
                                                })
                                                .done(function(data) {
                                                    if (data.price != null) {
                                                        $(".type_price_reaction").val(data.price);
                                                        $(".type_price_reaction_text").text(data.price);
                                                        $(".type_price_reaction_next").val(data.price_next);
                                                        $(".type_price_reaction_next_text") .text(data.price_next);

                                                        var per = ((data.price_next / data.price) * 100) - 100;
                                                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                        $(".price-current_reaction_per").text(roundedPer);
                                                    } else {
                                                        $(".type_price_reaction").val('');
                                                        $(".type_price_reaction_text").text('');
                                                        $(".type_price_reaction_next").val('');
                                                        $(".type_price_reaction_next_text").text('');
                                                        $(".price-current_reaction_per").text('0');
                                                    }
                                                });

                                                var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                                var type2 = 'Survey';
                                                $.ajax({
                                                    url: "{{ url('get-admin-pricing') }}",
                                                    data: {
                                                        media: media2,
                                                        type: type2
                                                    }
                                                })
                                                .done(function(data) {
                                                    if (data.price != null) {
                                                        $(".type_price_survey").val(data.price);
                                                        $(".type_price_survey_text").text(data.price);
                                                        $(".type_price_survey_next").val(data.price_next);
                                                        $(".type_price_survey_next_text").text(data.price_next);

                                                        var per = ( (data.price_next/data.price)*100 )-100;
                                                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                        $(".price-current_survey_per").text(roundedPer);
                                                    } else {
                                                        $(".type_price_survey").val('');
                                                        $(".type_price_survey_text").text('');
                                                        $(".type_price_survey_next").val('');
                                                        $(".type_price_survey_next_text").text(
                                                            '');
                                                        $(".price-current_survey_per").text('0');
                                                    }
                                                });
                                            }

                                            if ($(this).prop('checked') == false) {
                                                $(this).closest(".d-flex")
                                                    .find(".react-action-price-one")
                                                    .addClass("disabled-input");

                                                $(this).closest(".d-flex")
                                                    .find(".react-action-price-one")
                                                    .find("input").val("");

                                                $(this).closest(".d-flex")
                                                    .find(".react-action-price-one.disable")
                                                    .find("input")
                                                    .val("");
                                            }
                                        });
                                    }

                                    $(document).on('change', '.shareconent', function() {
                                        var mediaLink = $(this).val();
                                        $(this).closest(".select_media ").addClass(mediaLink);

                                        if ($(this).val() == "facebook") {
                                            $(this).closest(".select_media ").removeClass(
                                                "instagram twitter tiktok youtube snapchat twitch")
                                        } else if ($(this).val() == "twitch") {
                                            $(this).closest(".select_media ").removeClass(
                                                "instagram facebook twitter tiktok youtube snapchat"
                                                )
                                        } else if ($(this).val() == "instagram") {
                                            $(this).closest(".select_media ").removeClass(
                                                "facebook twitter tiktok youtube snapchat twitch")
                                        } else if ($(this).val() == "twitter") {
                                            $(this).closest(".select_media ").removeClass(
                                                "instagram facebook tiktok youtube snapchat twitch")
                                        } else if ($(this).val() == "tiktok") {
                                            $(this).closest(".select_media ").removeClass(
                                                "instagram twitter facebook youtube snapchat twitch"
                                                )
                                        } else if ($(this).val() == "youtube") {
                                            $(this).closest(".select_media ").removeClass(
                                                "instagram twitter tiktok facebook snapchat twitch")
                                        } else if ($(this).val() == "snapchat") {
                                            $(this).closest(".select_media ").removeClass(
                                                "instagram twitter tiktok youtube facebook twitch")
                                        }
                                    });

                                    $('.shareconent').each(function() {
                                        var mediaLink = $(this).val();
                                        $(this).closest(".select_media ").addClass(mediaLink);
                                    });
                                });

                            }

                            $(this).closest('.steps_con').hide();
                            $(".ser_op").removeClass("current");
                            if(target=="collection1") {
                                @if(isset($social_connect_media) &&  $social_connect_media->count()>0)
                                    $(".ser_op[data-title=" + target + "]").addClass("current").removeClass("disabled");
                                @else
                                    $(".ser_op[data-title=" + target + "]").addClass("current");
                                @endif
                            } else {
                                $(".ser_op[data-title=" + target + "]").addClass("current").removeClass("disabled");
                            }

                            $(".steps_con[data-content=" + target + "]").show();
                        } else {
                            $(this).closest("form").submit();
                        }
                    })
                } else {
                    if ($("#collection1 .connectWith .connectWithInner").hasClass("disconnectedToSocial" )) {
                        $(this).closest('.steps_con').hide();
                        $(".ser_op").removeClass("current");
                        $(".ser_op[data-title=" + target + "]").addClass("current").removeClass("disabled");
                        $(".steps_con[data-content=" + target + "]").show();
                    } else {
                        $('#errorText').html('<div class="worning_point">Please connect at least one social media to proceed</div>');
                        $("#collection1 .next").addClass("disabled")
                    }
                }
            } else if (safeVal("#stripe_status") == "not_connected") {
                $('#stripeError').html('<div class="worning_point">Please connect with Stripe before proceeding to the next step</div>');
                $(".ser_op").addClass("disabled");
                $('html, body').animate({ scrollTop: 0 }, 'slow');
            }
    });

    $(document).on("change", ".campaign-type-content input", function() {
        var media = $(this).attr("data-media");
        if ($(this).attr("name", "type_reaction").is(':checked')) {
            var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
            var type1 = 'Reaction video';
            $.ajax({
                    url: "{{ url('get-admin-pricing') }}",
                    data: {
                        media: media1,
                        type: type1
                    }
                })
                .done(function(data) {
                    if (data.price != null) {
                        $(".type_price_reaction").val(data.price);
                        $(".type_price_reaction_text").text(data.price);
                        $(".type_price_reaction_next").val(data.price_next);
                        $(".type_price_reaction_next_text").text(data.price_next);

                        var per = ((data.price_next / data.price) * 100) - 100;
                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                        $(".price-current_reaction_per").text(roundedPer);
                    } else {
                        $(".type_price_reaction").val('');
                        $(".type_price_reaction_text").text('');
                        $(".type_price_reaction_next").val('');
                        $(".type_price_reaction_next_text").text('');
                        $(".price-current_reaction_per").text('0');
                    }
                });

        }

        if ($(this).attr("name", "type_boost").is(':checked')) {
            var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
            var type = 'Boost me';
            $.ajax({
                    url: "{{ url('get-admin-pricing') }}",
                    data: {
                        media: media,
                        type: type
                    }
                })
                .done(function(data) {
                    if (data.price != null) {
                        $(".type_price_boost").val(data.price);
                        $(".type_price_boost_text").text(data.price);
                        $(".type_price_boost_next").val(data.price_next);
                        $(".type_price_boost_next_text").text(data.price_next);

                        var per = ((data.price_next / data.price) * 100) - 100;
                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                        $(".price-current_boost_per").text(roundedPer);
                    } else {
                        $(".type_price_boost").val('');
                        $(".type_price_boost_text").text('');
                        $(".type_price_boost_next").val('');
                        $(".type_price_boost_next_text").text('');
                        $(".price-current_boost_per").text('0');
                    }
                });
        }

        if ($(this).attr("name", "type_survey").is(':checked')) {
            var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
            var type2 = 'Survey';
            $.ajax({
                    url: "{{ url('get-admin-pricing') }}",
                    data: {
                        media: media2,
                        type: type2
                    }
                })
                .done(function(data) {
                    if (data.price != null) {
                        $(".type_price_survey").val(data.price);
                        $(".type_price_survey_text").text(data.price);
                        $(".type_price_survey_next").val(data.price_next);
                        $(".type_price_survey_next_text").text(data.price_next);

                        var per = ((data.price_next / data.price) * 100) - 100;
                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                        $(".price-current_survey_per").text(roundedPer);
                    } else {
                        $(".type_price_survey").val('');
                        $(".type_price_survey_text").text('');
                        $(".type_price_survey_next").val('');
                        $(".type_price_survey_next_text").text('');
                        $(".price-current_survey_per").text('0');
                    }
                });

        }
    })

    $("input.classtop").change(function() {
        var valueName = $(this).val();
        if ($(this).is(':checked')) {
            if ($(this).val() == "video_on_demand") {
                $(".socialMediaSection input[data-id= video_on_demand]").attr("disabled", false);
            } else if ($(this).val() == "livestream") {
                $(".socialMediaSection input[data-id=livestream]").attr("disabled", false);
            } else if ($(this).val() == "shareContent") {
                $(".socialMediaSection input[data-id= shareContent]").attr("disabled", false);
            }

            $(this).closest(".select_media").addClass("checkedmedia");
        } else {
            $(".socialMediaSection input[data-id=" + valueName + "]").attr("disabled", true).attr("checked", false);
            $(this).closest(".select_media").removeClass("checkedmedia snapchat instagram twitter tiktok youtube facebook twitch");
        }

    });

    $('input.classtop').each(function() {
        var valueName = $(this).val();
        if ($(this).is(':checked')) {
            if ($(this).val() == "video_on_demand") {
                $(".socialMediaSection input[data-id= video_on_demand]").attr("disabled", false);

            } else if ($(this).val() == "livestream") {
                $(".socialMediaSection input[data-id=livestream]").attr("disabled", false);


            } else if ($(this).val() == "shareContent") {
                $(".socialMediaSection input[data-id= shareContent]").attr("disabled", false);

            }
            $(this).closest(".select_media").addClass("checkedmedia");
        } else {
            $(".socialMediaSection input[data-id=" + valueName + "]").attr("disabled", true).attr("checked",
                false);
            $(this).closest(".select_media").removeClass(
                "checkedmedia snapchat instagram twitter tiktok youtube facebook twitch");
        }
    });
    $(".shareconent").change(function() {
        $(this).closest(".select_media").removeClass(
            "snapchat instagram twitter tiktok youtube facebook twitch");
        var valueName = $(this).val();
        $(this).closest(".select_media").addClass(valueName);
    });

    $('.shareconent').each(function() {
        if ($(this).val() == "") {
            $(this).closest(".select_media").removeClass(
                "snapchat instagram twitter tiktok youtube facebook twitch");
        }
    });

    function formatState(state) {
        if (!state.id) {
            return state.text;
        }
        var baseUrl = "{{ asset('/assets/front-end/images') }}";
        var $state = $(
            '<span><img src="' + baseUrl + '/col_icon_' + state.element.value.toLowerCase() +
            '.png" class="img-flag" /> ' + state.text + '</span>'
        );
        return $state;
    };

    $("form input, form select").change(function() {
        var form_id = $(this).closest(".steps_con").attr("data-content");
        $("#formId").val(form_id);
    });

    $(document).on("click", ".ser_op", function(e) {
        var mainTarget = $(".ser_op.current").attr("data-title");
        var dataContent = $(this).attr("data-title").replace(/collection/, '');
        if (mainTarget != 'collection1') {
            $("#"+mainTarget).find("form").each(function(){
                $("#"+mainTarget).find("form").parsley().validate()
                if ($("#"+mainTarget).find("form").parsley().isValid()) {
                    $(".ser_op").removeClass("current");
                    $("[data-title=collection"+dataContent+"]").addClass("current");
                    $(".steps_con").removeClass("formActive").hide();
                    $("#collection"+dataContent).addClass("formActive").show();
                    if (mainTarget == 'collection0') {
                        formData = $('#draft0').serialize();
                        $.ajax({
                            url: "{{ url('/my-profile') }}",
                            type: "post",
                            dataType: 'json',
                            data: formData,
                        }).done(function(data) {
                            // Profile saved successfully
                        })
                    }else if (mainTarget == 'collection2') {
                        var hashtag1 = safeVal("#hashtag1");
                        var hashtag2 = safeVal("#hashtag2");
                        var hashtag3 = safeVal("#hashtag3");

                        if (hashtag1 === hashtag2 || hashtag1 === hashtag3 || hashtag2 === hashtag3) {
                            $(".hashtagerror").show();
                            return false;
                        }else {
                            $(".hashtagerror").hide();
                        }
                        formData = $('#draft1').serialize();
                    } else if (mainTarget == 'collection3') {
                        formData = $('#draft2').serialize();
                    } else if (mainTarget == 'collection4') {
                        formData = $('#draft3').serialize();

                    } else if (mainTarget == 'collection5') {
                        formData = $('#draft4').serialize();
                    }
                    if (formData != null) {
                        $.ajax({
                            url: "{{ url('/save-influencer-form') }}",
                            type: "post",
                            dataType: 'json',
                            data: formData,
                        }).done(function(data) {
                            if (data.status == true) {
                                if($("#collection2").find('form').data('changed')) {
                                    //do something
                                    $('.generalInfo').html(data.generalPage);
                                }
                                $(".targetGroup").html(data.targetPage);
                                $(".advertisingMethod1").html(data.advertisingPage);
                                $(".advertisingPriceMethod1").html(data.advertisingPricePage);
                                $(".connectPublish").html(data.publishPage);
                                if ($("select[name=sharecontent_media]").val() == "") {
                                    $("select[name=sharecontent_media]").closest(".select_media").find(
                                        "input[type=checkbox]").prop('checked', false);
                                    $("select[name=sharecontent_media]").prop('disabled', true);
                                    $("select[name=sharecontent_media]").closest(".select_media")
                                        .removeClass("checkedmedia")
                                }
                                if ($("select[name=video_media]").val() == "") {
                                    $("select[name=video_media]").closest(".select_media").find(
                                        "input[type=checkbox]").prop('checked', false);
                                    $("select[name=video_media]").prop('disabled', true);
                                    $("select[name=video_media]").closest(".select_media").removeClass(
                                        "checkedmedia")
                                }
                                if ($("select[name=livestream_media]").val() == "") {
                                    $("select[name=livestream_media]").closest(".select_media").find(
                                        "input[type=checkbox]").prop('checked', false);
                                    $("select[name=livestream_media]").prop('disabled', true);
                                    $("select[name=livestream_media]").closest(".select_media")
                                        .removeClass("checkedmedia")
                                }

                                $(".select").select2();
                                $('div[data-title=' + mainTarget + ']').removeClass("disabled");
                                var isValid = true;
                                $('div[data-content=' + mainTarget + '] .form-control[required]').each(
                                function() {
                                    if ($(this).val() === '') {
                                        isValid = false;
                                        event.stopPropagation();
                                    }
                                });

                                $("input.classtop").change(function() {
                                    var valueName = $(this).val();
                                    if ($(this).is(':checked')) {
                                        if ($(this).val() == "video_on_demand") {
                                            $(".socialMediaSection input[data-id= video_on_demand]")
                                                .attr("disabled", false);

                                        } else if ($(this).val() == "livestream") {
                                            $(".socialMediaSection input[data-id=livestream]")
                                                .attr("disabled", false);


                                        } else if ($(this).val() == "shareContent") {
                                            $(".socialMediaSection input[data-id= shareContent]")
                                                .attr("disabled", false);

                                        }
                                        $(this).closest(".select_media").addClass(
                                            "checkedmedia");
                                    } else {
                                        $(".socialMediaSection input[data-id=" + valueName +
                                            "]").attr("disabled", true).attr("checked",
                                            false);
                                        $(this).closest(".select_media").removeClass(
                                            "checkedmedia snapchat instagram twitter tiktok youtube facebook twitch"
                                            );
                                    }

                                });

                                var media = $('input[name="media"]:checked').val();
                                var type_count = @json($user);

                                var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                var type = 'Boost me';
                                $.ajax({
                                        url: "{{ url('get-admin-pricing') }}",
                                        data: {
                                            media: media,
                                            type: type
                                        }
                                    })
                                    .done(function(data) {
                                        if (data.price != null) {
                                            $(".type_price_boost").val(data.price);
                                            $(".type_price_boost_text").text(data.price);
                                            $(".type_price_boost_next").val(data.price_next);
                                            $(".type_price_boost_next_text").text(data.price_next);

                                            var per = ( (data.price_next / data.price) * 100) - 100;
                                            const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                            $(".price-current_boost_per").text(roundedPer);
                                        } else {
                                            $(".type_price_boost").val('');
                                            $(".type_price_boost_text").text('');
                                            $(".type_price_boost_next").val('');
                                            $(".type_price_boost_next_text").text('');
                                            $(".price-current_boost_per").text('0');
                                        }
                                    });


                                var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                var type1 = 'Reaction video';
                                $.ajax({
                                    url: "{{ url('get-admin-pricing') }}",
                                    data: {
                                        media: media1,
                                        type: type1
                                    }
                                })
                                .done(function(data) {
                                    if (data.price != null) {
                                        $(".type_price_reaction").val(data.price);
                                        $(".type_price_reaction_text").text(data.price);
                                        $(".type_price_reaction_next").val(data.price_next);
                                        $(".type_price_reaction_next_text").text(data.price_next);

                                        var per = ((data.price_next / data.price) * 100) - 100;
                                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                        $(".price-current_reaction_per").text(roundedPer);
                                    } else {
                                        $(".type_price_reaction").val('');
                                        $(".type_price_reaction_text").text('');
                                        $(".type_price_reaction_next").val('');
                                        $(".type_price_reaction_next_text").text('');
                                        $(".price-current_reaction_per").text('0');
                                    }
                                });



                                var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                var type2 = 'Survey';
                                $.ajax({
                                    url: "{{ url('get-admin-pricing') }}",
                                    data: {
                                        media: media2,
                                        type: type2
                                    }
                                })
                                .done(function(data) {
                                    if (data.price != null) {
                                        $(".type_price_survey").val(data.price);
                                        $(".type_price_survey_text").text(data.price);
                                        $(".type_price_survey_next").val(data.price_next);
                                        $(".type_price_survey_next_text").text(data.price_next);

                                        var per = ((data.price_next / data.price) * 100) - 100;
                                        const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                        $(".price-current_survey_per").text(roundedPer);
                                    } else {
                                        $(".type_price_survey").val('');
                                        $(".type_price_survey_text").text('');
                                        $(".type_price_survey_next").val('');
                                        $(".type_price_survey_next_text").text('');
                                        $(".price-current_survey_per").text('0');
                                    }
                                });


                                $(".campaign-type-content input[type=checkbox]").each(function() {
                                    var media = $(".campaign-type-content input").attr(
                                        "data-media");
                                    if ($(this).is(':checked')) {


                                        var media = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type = 'Boost me';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media,
                                                    type: type
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_boost").val(data.price);
                                                    $(".type_price_boost_text").text(data.price);
                                                    $(".type_price_boost_next").val(data.price_next);
                                                    $(".type_price_boost_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_boost_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_boost").val('');
                                                    $(".type_price_boost_text").text('');
                                                    $(".type_price_boost_next").val('');
                                                    $(".type_price_boost_next_text").text(
                                                        '');
                                                    $(".price-current_boost_per").text('0');
                                                }
                                            });


                                        var media1 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type1 = 'Reaction video';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media1,
                                                    type: type1
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_reaction").val(data.price);
                                                    $(".type_price_reaction_text").text(data.price);
                                                    $(".type_price_reaction_next").val(data.price_next);
                                                    $(".type_price_reaction_next_text") .text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_reaction_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_reaction").val('');
                                                    $(".type_price_reaction_text").text('');
                                                    $(".type_price_reaction_next").val('');
                                                    $(".type_price_reaction_next_text").text('');
                                                    $(".price-current_reaction_per").text('0');
                                                }
                                            });



                                        var media2 = @json(isset($social_connect_high) ? $social_connect_high['media'] : 'facebook');
                                        var type2 = 'Survey';
                                        $.ajax({
                                                url: "{{ url('get-admin-pricing') }}",
                                                data: {
                                                    media: media2,
                                                    type: type2
                                                }
                                            })
                                            .done(function(data) {
                                                if (data.price != null) {
                                                    $(".type_price_survey").val(data.price);
                                                    $(".type_price_survey_text").text(data.price);
                                                    $(".type_price_survey_next").val(data.price_next);
                                                    $(".type_price_survey_next_text").text(data.price_next);

                                                    var per = ((data.price_next / data.price) * 100) - 100;
                                                    const roundedPer = data.price > 0 ? Math.round(per) : 0;
                                                    $(".price-current_survey_per").text(roundedPer);
                                                } else {
                                                    $(".type_price_survey").val('');
                                                    $(".type_price_survey_text").text('');
                                                    $(".type_price_survey_next").val('');
                                                    $(".type_price_survey_next_text").text(
                                                        '');
                                                    $(".price-current_survey_per").text('0');
                                                }
                                            });

                                    }
                                    if ($(this).prop('checked') == false) {
                                        $(this).closest(".d-flex").find(
                                            ".react-action-price-one").addClass(
                                            "disabled-input");
                                        $(this).closest(".d-flex").find(
                                            ".react-action-price-one").find("input").val("")
                                        $(this).closest(".d-flex").find(
                                                ".react-action-price-one.disable").find("input")
                                            .val("")
                                    }
                                })


                            }
                            $(document).on('change', '.shareconent', function() {
                                var mediaLink = $(this).val();
                                $(this).closest(".select_media ").addClass(mediaLink);

                                if ($(this).val() == "facebook") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram twitter tiktok youtube snapchat twitch")
                                } else if ($(this).val() == "twitch") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram facebook twitter tiktok youtube snapchat"
                                        )
                                } else if ($(this).val() == "instagram") {
                                    $(this).closest(".select_media ").removeClass(
                                        "facebook twitter tiktok youtube snapchat twitch")
                                } else if ($(this).val() == "twitter") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram facebook tiktok youtube snapchat twitch")
                                } else if ($(this).val() == "tiktok") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram twitter facebook youtube snapchat twitch"
                                        )
                                } else if ($(this).val() == "youtube") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram twitter tiktok facebook snapchat twitch")
                                } else if ($(this).val() == "snapchat") {
                                    $(this).closest(".select_media ").removeClass(
                                        "instagram twitter tiktok youtube facebook twitch")
                                }
                            });
                            $('.shareconent').each(function() {
                                var mediaLink = $(this).val();
                                $(this).closest(".select_media ").addClass(mediaLink);
                            });
                        });

                    }
                }
            })
        }else{
            $(".ser_op").removeClass("current");
            $("[data-title=collection"+dataContent+"]").addClass("current");
            $(".steps_con").removeClass("formActive").hide();
            $("#collection"+dataContent).addClass("formActive").show();
        }
    });

    $(document).on("click", "#draft1 [type=submit]", function(e) {
        var hashtag1 = safeVal("#hashtag1");
        var hashtag2 = safeVal("#hashtag2");
        var hashtag3 = safeVal("#hashtag3");

        if (hashtag1 === hashtag2 || hashtag1 === hashtag3 || hashtag2 === hashtag3) {
            $(".hashtagerror").show();
            e.preventDefault();
            return false;
        } else {
            $(".hashtagerror").hide();
        }

    })
    $("#draft1").submit(function() {
        $('.tagify--empty').css("border-color", "red")
        var hashtag1 = safeVal("#hashtag1");
        var hashtag2 = safeVal("#hashtag2");
        var hashtag3 = safeVal("#hashtag3");

        if (hashtag1 == "") {
            $("#hashtag1").closest(".input-group").addClass("eror")
        } else {
            $("#hashtag1").closest(".input-group").removeClass("eror")
        }
        if (hashtag2 == "") {
            $("#hashtag2").closest(".input-group").addClass("eror")
        } else {
            $("#hashtag2").closest(".input-group").removeClass("eror")
        }
        if (hashtag3 == "") {
            $("#hashtag3").closest(".input-group").addClass("eror")
        } else {
            $("#hashtag3").closest(".input-group").removeClass("eror")
        }
    })

    function confirmSubmit() {
        var count;
        var countPrice;
        $.ajax({
                url: "{{ url('get-social-connect') }}",
            })
            .done(function(data) {
                if (data.social_connect == null) {
                    $('#errorText').html(
                        '<div class="worning_point">Please connect at least one social media to proceed</div>'
                        );
                    $("#collection1 .next").addClass("disabled")
                    count = 1;
                } else {
                    count = 0;
                }
            });

        if (count != 1) {
            countPrice = 0;
            var values = $("input[name='price[]']").map(function() {
                return $(this).val();
            }).get();
            $.each(values, function(index, value) {
                if (value > 0) {
                    countPrice = 1;
                }
            });
            if (countPrice == 0) {
                count = 1;

                // $('#confirmreset').modal('show');
                $('#errorText').html('Please select atleast one of Advertising Methods and its price should be more than 0$!');
            } else {
                count = 0;
            }
        }

        if (count == 0) {
            return true;
        } else {
            return false;
        }
    }

    function confirmReset() {
        var agree = confirm("If you save in draft-mode, you will be not listed in marketplace.");
        if (agree)
            return true;
        else
            return false;
    }

    $('#selectCountry').change(function() {
        $("#selectCity").empty();
        var countryVal = safeVal(this);
        if (countryVal) {
            $.get("{{ url('fetch-states') }}", {
                country: countryVal
            }, function(res) {
                $("#selectCity").append(res);
            });
        }
    });

    $('#selectState').change(function() {
        $("#selectCity").empty();
        var stateVal = safeVal(this);
        if (stateVal) {
            $.get("{{ url('fetch-cities') }}", {
                state: stateVal
            }, function(res) {
                $("#selectCity").append(res);
                $("#selectCity").selectpicker('refresh');
            });
        }
    });


    function validate(e) {
        var text = e.target.value;

        text = text.substring(1);
        $.ajax({
            type: "GET",
            url: "{{ URL::to('/hashtags') }}/" + text,
            dataType: "json",
        }).done(function(response) {
            if (response && Array.isArray(response)) {
                var len = response.length;
                $("#searchResult").empty();
                for (var i = 0; i < len; i++) {
                    var tags = response[i];
                    $("#searchResult").append("<li >" + tags + "</li>");
                }
            }
        });
    }

    $("input[name='influencer_type']").change(function() {
        if (safeVal("input[type='radio'].influencer_type:checked") == 'Real personality') {
            $(".gender").attr('required', 'true');
            $("#viewgender").show();
        } else {
            $(".gender").removeAttr('required');
            $("#viewgender").hide();
        }
    });

    $(document).ready(function(event) {
        $(document).on('submit', '#configuration', function(e) {
            var formData = new FormData($(this)[0]);
            $.ajax({
                type: "POST",
                url: "{{ URL::to('/influencer') }}",
                data: formData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(data) {
                    $(".wizardPopupOuter").hide();
                    toastr.success(data.msg);

                    $("#javascript_alert_msg").css("display", "block");
                    $("#javascript_alert_msg").slideDown().html(
                        '<div class="alert alert-success custom-alert-success animated  flash  text-center" style="margin-top: 38px!important;">' +
                        data.msg + ' </div>');
                    $("body").removeClass("hidescroll")
                }
            });

        });


        if (safeVal("select[name=sharecontent_media]") == "" && safeVal("select[name=video_media]") == "" && safeVal("select[name=livestream_media]") == "") {
            $("div[data-title=collection3]").addClass("disabled")
            $("div[data-title=collection4]").addClass("disabled")
            $("div[data-title=collection5]").addClass("disabled")
        }

        if(safeVal("#stripe_status") == "connected") {
            $("#tablink0").removeClass("disabled");

            $("#collection0 [required]").each(function(){
                var val = safeVal(this);
                if (val != null && val != "" && val != 0 && val != " ") {
                    @if((isset($social_connect_media) && $social_connect_media->count()>0))
                        $("#tablink1").removeClass("disabled")
                    @endif
                }
            })
            $("#collection2 [required]").each(function(){
                var val = $("#collection2 [required]").val();
                if (val && val.length > 0) {
                    $("#tablink2").removeClass("disabled")
                }
            })
            $("#collection3 [required]").each(function(){
                var val = $("#collection3 [required]").val();
                if (val && val.length > 0) {
                    $("#tablink3").removeClass("disabled")
                }
            })

            var collection4Val = $("#collection4 [name=collection]").val();
            if (collection4Val && collection4Val.length > 0) {
                $("#tablink4").removeClass("disabled")
            }
            var collection5Val = $("#collection5 [name=collection]").val();
            if (collection5Val && collection5Val.length > 0) {
                $("#tablink5").removeClass("disabled")
            }
        }
    });

    function refreshParent() {
        $("#errorMji").hide();
        $.ajax({
            url: "{{ url('latest-social-connect') }}",
        })
        .done(function(data) {
            $(".connectWith").html(data);
        });
        $.ajax({
            url: "{{ url('latest-advertising') }}",
        })
        .done(function(data) {
            $(".connectPrising").html(data);
        });
    }


    function refreshParentError() {
        refreshParent();
        $("#errorMji").show();
        $(".closeErrorNow").click(function() {
            $("#errorMji").hide();
            {{ Session::forget('influencer_error') }}
            {{ Session::forget('influencer_follower_error') }}
        });
    }

    $(function() {
        $('#zip_code').on('keypress', function(e) {
            if (e.which == 32) {
                return false;
            }
        });
    });

    $(document).click(function(event) {
        var nextlink = $(".nextLinkReq");
        if (nextlink && nextlink.length > 0) {
            var hasTarget = nextlink.has(event.target);
            var formControl = nextlink.closest(".floating-label").find(".form-control");
            var formControlHas = formControl.has(event.target);
            var select2Container = nextlink.closest(".floating-label").find(".select2-container");
            var select2ContainerHas = select2Container.has(event.target);

            if (!nextlink.is(event.target) &&
                (!hasTarget || hasTarget.length === 0) &&
                !formControl.is(event.target) &&
                (!formControlHas || formControlHas.length === 0) &&
                !select2Container.is(event.target) &&
                (!select2ContainerHas || select2ContainerHas.length === 0)) {
                $(".nextLinkReq").hide();
                $(".nextLinkOnl").hide();
            }
        }
    });

    $(document).ready(function() {
        $.ajax({
            url: "{{ url('get-influencer-data') }}",
        })
        .done(function(data) {
            // Data received successfully
        });



        var collection = "{{ isset($collection) ? $collection : 0 }}";
        if (parseInt(collection) > 0) {
            $(".next").closest('.steps_con').hide().removeClass("formActive");
            $(".ser_op").removeClass("current");
            $(".ser_op[data-title=collection" + collection + "]").addClass("current");
            $(".steps_con[data-content=collection" + collection + "]").show().addClass("formActive");
        } else {
            $(".steps_con").first().show().addClass("formActive");
            $(".ser_op").first().addClass("current");
        }

        $(window).keydown(function(event) {
            if (event.keyCode == 13) {
                event.preventDefault();
                return false;
            }
        });
    });

    $('#draft1').submit(function(e) {
        if ($("#hashtag1").hasClass("parsley-error")) {
            $("#hashtag1").closest(".input-group").addClass("errorDiv")
        } else {
            $("#hashtag1").closest(".input-group").removeClass("errorDiv")
        }
    });

    $(document).ready(function() {
        $('.shareconent').each(function() {
            var mediaLink = $(this).val();
            $(this).closest(".select_media ").addClass(mediaLink);
        });
    });

    $(document).on("click", ".back", function() {
        var step_id = $(this).closest(".steps_con").attr("id").replace(/collection/, '');
        $("#tablink" + step_id).removeClass("current");
        $("#tablink" + (step_id - 1)).addClass("current");
        $("#collection" + step_id).hide();
        $("#collection" + (step_id - 1)).show();
    })

    $(document).on("click", ".campaign-type-content input", function() {
        var get_class = $(this).closest(".campaign-type").attr("class").replace(
            "social_media_radio campaign-type ", "")
        if ($(this).is(":checked")) {
            $(".d-flex." + get_class).find(".react-action-price-one input.inputval").prop("disabled", false)
            $(".d-flex." + get_class).find(".react-action-price-one").removeClass("disabled-input")
            $(".d-flex." + get_class).find("[type=checkbox]").prop("checked", true)

        } else {
            $(".d-flex." + get_class).find(".react-action-price-one input.inputval").prop("disabled", true)
            $(".d-flex." + get_class).find(".react-action-price-one").addClass("disabled-input")
            $(".d-flex." + get_class).find("[type=checkbox]").prop("checked", false)
        }
        var checkedInputs = $("#collection3 .selected-media-action input[type='checkbox']:checked");
        if (checkedInputs && checkedInputs.length == 0) {
            $("#tablink4").addClass("disabled")
        }
    })
    $('#rqrIn3country').select2().change(function(){
        if( safeVal(this) == '') {
            $("#rqrIn5").prop('disabled', true);;
        } else {
            $("#rqrIn5").removeAttr("disabled");
        }

        $("#rqrIn5").empty();

        $.get("{{url('fetch-states')}}", {country: safeVal(this)}, function(res){
            $("#rqrIn5").append(res);
            $("#rqrIn5").select2('refresh');
        });
    });
</script>
@endsection
