
@php

$Influencer = App\Models\InfluencerDetail::where('user_id',Auth::id())->first(); 

$tot_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id',@$Influencer->id)
                ->where('finish',NULL)->where('refund_reason',NULL)
                ->where(function ($query) {
                    $query->where('review', '!=', 1) ;
                })
                ->count(); 
$open_count =   App\Models\InfluencerRequestDetail::join('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                 ->select('influencer_request_details.*')
                 ->where('influencer_request_details.influencer_detail_id',@$Influencer->id)
                 ->where('influencer_request_details.finish',NULL)
                 ->where('influencer_request_details.refund_reason',NULL) 
                ->where(function ($query) {
                    $query->where('influencer_request_details.review', '=', NULL)
                          ->orWhere('influencer_request_details.review', '=', 0);
                })            
                 ->count();  


$req_count =$tot_count ; 


$myCampaignList =    App\Models\InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
             ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept','influencer_request_accepts.id as influencer_request_accept_id' ,'influencer_request_accepts.created_at as accept_time')
            ->where('influencer_details.user_id',Auth::id())
            ->orderBy('influencer_request_details.id','desc')->get();    

$postCount = 0 ; 
$postCountReq = 0 ; 
foreach($myCampaignList as $row){
    if($row->request == 1 &&  $row->invoice_id!='' && $row->review !='1' && $row->review !='0'  &&   $row->refund_reason =='' ){ 
            $postCountReq++;
            if($row->invoice_id!=''){
                $postCount++;
            } 
    }

}  

$reqCountList =   App\Models\InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
             ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept','influencer_request_accepts.id as influencer_request_accept_id' )
            ->where('influencer_details.user_id',Auth::id())
            ->orderBy('influencer_request_details.id','desc')->get() ;

$campaignRequestTime = App\Models\CampaignRequestTime::first();            

 $reqCount = 0 ;
 $openCountReq = 0 ;
foreach($reqCountList as $row){
    if($row->invoice_id=='' && $row->review != '2' && $row->refund_reason == ''){ 
        $time = (isset($row->request_time_accept) && $row->request_time_accept == 1)?$row->request_time+$row->time:$row->time;

        $created_date =  date('Y-m-d H:i:s',strtotime($row->created_at));
        $updated_date =  date('Y-m-d H:i:s',strtotime($row->updated_at));
        $campaignDate= date('Y-m-d H:i:s', strtotime($created_date. ' + '.$campaignRequestTime->request_time.' days'));  
        $date = date('Y-m-d H:i:s'); 
        $seconds = strtotime($campaignDate) - strtotime($date);

        $days    = floor($seconds / 86400);  
        if($date <= $campaignDate ){  
                $openCountReq++; 
                $reqCount++; 
        }
    }

}
$tot = $reqCount; 
$req_count = $postCountReq+$openCountReq;
@endphp
<div class="connectPrising advertisingSelect ">
    @if ($user->activate != '2')
        <input type="hidden" name="collection"  value="">  
    @else
        <input type="hidden" name="collection"  value="4">  
    @endif
    <input type="hidden" name="check-status"  value="{{ (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'facebook') || (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'instagram') || (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'tiktok') || (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'youtube') ?'selected':'' }}">  

    <div class="publish-outer">
        <div class="active-inactive">
            <div class="active-inactive-inner">
                {{-- <label>You are</label> --}}
                <div class="active-inactive-input">
                    <input type="checkbox" name="status" id="user_status" value="1" @if(Auth::user()->status==1 && $req_count < 5) checked @endif >
                    <div class="input-label">
                        <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-wifi.svg" class="" alt="">
                        <span class="active-inactive-text"></span>
                    </div>
                </div>
            </div>
        </div>

        {{-- <div class="am-selected-media">
            <ul class="media-box nav nav-tabs" role="tablist">
                @php $count_social = 0;  $social_selected = ''; @endphp
                @foreach($newSocialConnectArr as $key => $socialArr)
                    <?php 
                        $image = asset('/').'/assets/front-end/images/icons/new_social_media_facebook.png';

                        if($key == 'facebook')
                        {
                            $image = asset('/').'/assets/front-end/images/icons/new_social_media_facebook.png';
                        }elseif($key == 'instagram'){
                            $image = asset('/').'/assets/front-end/images/icons/new_social_media_instagram.png';
                        }elseif($key == 'tiktok'){
                            $image = asset('/').'/assets/front-end/images/icons/new_social_media_tiktok.png';
                        }elseif($key == 'youtube'){
                            $image = asset('/').'/assets/front-end/images/icons/new_social_media_youtube.png';

                        }elseif($key == 'twitter'){
                            $image = asset('/').'/assets/front-end/images/icons/new_social_media_twitter.png';
                        }

                    ?>
                    <li class="" role="presentation">
                        <input type="checkbox" name="media" value="{{$key}}" class="mediaCheck required classtop custom-control-input"  data-parsley-errors-container="#error-select-media" id="{{$key}}-tab"  checked  required >
                        <label data-bs-toggle="tab" data-bs-target="#{{$key}}-publish" type="button" role="tab" aria-controls="{{$key}}" aria-selected="{{ ($count_social == 0)?'true':'false' }}" class="{{ ($count_social == 0)?'active':'' }}"><img src="{{ $image }}" class=" icon" alt=""></label>
                    </li>
                    @php 
                        if($count_social == 0){
                            $social_selected = $key ; 
                        }
                        $count_social++; 
                    @endphp
                @endforeach 
                 <!-- 
                     @if(isset($social_connect_facebook))
                    <li class="" role="presentation">
                        <input type="checkbox" name="media" value="facebook" class="mediaCheck required classtop custom-control-input"  data-parsley-errors-container="#error-select-media" id="facebook-tab" data-bs-toggle="tab" data-bs-target="#facebook" type="button" role="tab" aria-controls="facebook" aria-selected="{{ (isset($social_connect->media) && $social_connect->media == 'facebook')?'true':'false' }}" checked  required >
                        <label><img src="{{ asset('/') }}/assets/front-end/images/icons/new_social_media_facebook.png" class="{{ (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'facebook')?'active':'' }} icon" alt=""></label>
                    </li>
                    @endif
                     @if(isset($social_connect_instagram))
                    <li class="" role="presentation">
                        <input type="checkbox" name="media" value="instagram" class="mediaCheck required classtop custom-control-input"  id="instagram-tab" data-bs-toggle="tab" data-bs-target="#instagram" type="button" role="tab" aria-controls="instagram" aria-selected="{{ (isset($social_connect->media) && $social_connect->media == 'instagram')?'true':'false' }}" checked >
                        <label><img src="{{ asset('/') }}/assets/front-end/images/icons/new_social_media_instagram.png" class="{{ (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'instagram')?'active':'' }} icon" alt=""></label>
                    </li>
                    @endif
                     @if(isset($social_connect_tiktok))
                    <li class="" role="presentation">
                        <input type="checkbox" name="media" value="tiktok" class="mediaCheck required classtop custom-control-input"  id="tiktok-tab" data-bs-toggle="tab" data-bs-target="#tiktok" type="button" role="tab" aria-controls="tiktok" aria-selected="{{ (isset($social_connect->media) && $social_connect->media == 'tiktok')?'true':'false' }}"  checked >
                        <label><img src="{{ asset('/') }}/assets/front-end/images/icons/new_social_media_tiktok.png" class="{{ (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'tiktok')?'active':'' }} icon" alt=""></label>
                    </li>
                    @endif
                     @if(isset($social_connect_youtube))
                    <li class="" role="presentation">
                        <input type="checkbox" name="media" value="youtube" class="mediaCheck required classtop custom-control-input"  id="youtube-tab" data-bs-toggle="tab" data-bs-target="#youtube" type="button" role="tab" aria-controls="youtube" aria-selected="{{ (isset($social_connect->media) && $social_connect->media == 'youtube')?'true':'false' }}"  checked >
                        <label><img src="{{ asset('/') }}/assets/front-end/images/icons/new_social_media_youtube.png" class="{{ (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'youtube')?'active':'' }} icon" alt=""></label>
                    </li>
                    @endif
                     <li role="presentation"class="">
                        <img src="{{ asset('/') }}/assets/front-end/images/icons/new_social_media_twitter.png" class="icon" alt="" @if(!isset($social_connect_twitter)) disabled @endif  >
                    </li> 
                -->
            </ul>
        </div>

        <div class="tab-content" id="myTabContent">
            @foreach($advertising_methods as $advertise)
            @php 
                $advertising_method = \App\Models\AdvertisingMethodNewPrice::where('user_id',Auth::id())->where('media',$advertise->media)->get(); @endphp
            <div class="publish-last-div tab-pane fade {{ (isset($social_selected) && $social_selected == $advertise->media)?'show active':'' }}   " id="{{$advertise->media}}-publish" role="tabpanel" aria-labelledby="{{$advertise->media}}-publish-tab">

                @foreach($advertising_method as $adv)

                    @php $social_connect_media = \App\Models\SocialConnect::where('user_id',Auth::id())->where('media',$advertise->media)->first(); @endphp
                    @if($adv->type == 'Reaction video')
                    <div class="publish-info">
                        <div class="publish-info-content d-flex">
                            <div class="react-action">
                                <div class="react-action-name">
                                    <div class="social_media_radio campaign-type campaign-type-reaction-video">
                                        <div class="campaign-type-content">
                                            <label for="type-post-content-Reaction-video">
                                                <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-reaction-video.svg" class="" alt="">
                                                <div class="campaign-type-text">{{isset($adv->type)?$adv->type:''}}</div>
                                                <div class="campaign-type-share-information">Influencer reacts to your video</div>
                                            </label> 
                                        </div>
                                    </div>
                                </div> 
                            </div> 
                            <div class="influncer-detail" data-id="inflinser_id2">
                                <div class="influncerleft">
                                    <div class="influncer-image">
                                        <img src="{{asset('storage/'.(isset($social_connect_media)?$social_connect_media->picture:''))}}" class="" alt="">
                                    </div>
                                    <div class="influncer-flags">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-flage.png" class="" alt=""> 

                                        @php $userData =  \App\Models\User::where('id',Auth::user()->id)->first(); @endphp 
                                            <img src="{{ asset('/') }}/assets/front-end/images/icons/trofy-{{$userData->trophy}}.svg" alt=""> 
                                    </div>
                                </div>
                                <div class="influncerright">
                                    <div class="user-in">
                                        <span class="user_name">{{isset($social_connect_media)?$social_connect_media->name:''}}</span>
                                        <span class="follower-count">{{isset($social_connect_media)?$social_connect_media->followers:''}} Follower</span>
                                        <span class="user_price"> {{isset($adv->type)?$adv->type_price:''}} €</span>
                                    </div>
                                    <button type="button" class="select-button">Select</button>
                                </div>
                            </div> 
                        </div> 
                    </div>
                    @endif


                    @if($adv->type == 'Boost me')
                    <div class="publish-info">
                        <div class="publish-info-content d-flex">
                            <div class="react-action">
                                <div class="react-action-name">
                                    <div class="social_media_radio campaign-type campaign-type-reaction-video">
                                        <div class="campaign-type-content">
                                            <label for="type-post-content-Reaction-video">
                                                <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-boost-me.png" class="" alt="">
                                                <div class="campaign-type-text">{{isset($adv->type)?$adv->type:''}}</div>
                                                <div class="campaign-type-share-information">Influencer reacts to your video</div>
                                            </label>
                                            <!-- <div class="radio-content">
                                                <div class="form-group">
                                                    <input type="radio" name="mp-socialmedia-type2" value="Post - Picture" class="ds" data-parsley-multiple="mp-socialmedia-type2">
                                                    <label>Post - Picture</label>
                                                </div>
                                                <div class="form-group">
                                                    <input type="radio" name="mp-socialmedia-type2" value="Story - Picture" class="ds" data-parsley-multiple="mp-socialmedia-type2">
                                                    <label>Story - Picture</label>
                                                </div>
                                            </div> -->
                                        </div>
                                    </div>
                                </div> 
                            </div> 
                            <div class="influncer-detail" data-id="inflinser_id2">
                                        @php $userData =  \App\Models\User::where('id',Auth::user()->id)->first(); @endphp 
                                <div class="influncerleft">
                                    <div class="influncer-image">
                                        <img src="{{asset('storage/'.(isset($social_connect_media)?$social_connect_media->picture:''))}}" class="" alt="">
                                    </div>
                                    <div class="influncer-flags">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-flage.png" class="" alt="">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/trofy-{{$userData->trophy}}.svg" class="" alt=""> 
                                            
                                    </div>
                                </div>
                                <div class="influncerright">
                                    <div class="user-in">
                                        <span class="user_name">{{isset($social_connect_media)?$social_connect_media->name:''}}</span>
                                        <span class="follower-count">{{isset($social_connect_media)?$social_connect_media->followers:''}} Follower</span>
                                        <span class="user_price">  {{isset($adv->type)?$adv->type_price:''}} €</span>
                                    </div>
                                    <button type="button" class="select-button">Select</button>
                                </div>
                            </div>  
                        </div> 
                    </div>

                    @endif

                    @if($adv->type == 'Survey')
                    <div class="publish-info">
                        <div class="publish-info-content d-flex">
                            <div class="react-action">
                                <div class="react-action-name">
                                    <div class="social_media_radio campaign-type campaign-type-reaction-video">
                                        <div class="campaign-type-content">
                                            <label for="type-post-content-Reaction-video">
                                                <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-survey.png" class="" alt="">
                                                <div class="campaign-type-text">{{isset($adv->type)?$adv->type:''}}</div>
                                                <div class="campaign-type-share-information">Influencer reacts to your video</div>
                                            </label>
                                            <!-- <div class="radio-content">
                                                <div class="form-group">
                                                    <input type="radio" name="mp-socialmedia-type2" value="Post - Picture" class="ds" data-parsley-multiple="mp-socialmedia-type2">
                                                    <label>Post - Picture</label>
                                                </div>
                                                <div class="form-group">
                                                    <input type="radio" name="mp-socialmedia-type2" value="Story - Picture" class="ds" data-parsley-multiple="mp-socialmedia-type2">
                                                    <label>Story - Picture</label>
                                                </div>
                                            </div> -->
                                        </div>
                                    </div>
                                </div> 
                            </div> 
                            <div class="influncer-detail" data-id="inflinser_id2">
                                        @php $userData =  \App\Models\User::where('id',Auth::user()->id)->first(); @endphp 
                                <div class="influncerleft">
                                    <div class="influncer-image">
                                        <img src="{{asset('storage/'.(isset($social_connect_media)?$social_connect_media->picture:''))}}" class="" alt="">
                                    </div>
                                    <div class="influncer-flags">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-flage.png" class="" alt="">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/trofy-{{$userData->trophy}}.svg" class="" alt=""> 
                                            
                                    </div>
                                </div>
                                <div class="influncerright">
                                    <div class="user-in">
                                        <span class="user_name">{{isset($social_connect_media)?$social_connect_media->name:''}}</span>
                                        <span class="follower-count">{{isset($social_connect_media)?$social_connect_media->followers:''}} Follower</span>
                                        <span class="user_price">  {{isset($adv->type)?$adv->type_price:''}} €</span>
                                    </div>
                                    <button type="button" class="select-button">Select</button>
                                </div>
                            </div>  
                        </div> 
                    </div>

                    @endif
                @endforeach 

            </div>
            @endforeach 
        </div> --}}
        
        <div id="usr_online" @if(!(Auth::user()->status==1 && $req_count < 5)) style="display: none;" @endif>
            <div class="row pt-5 justify-content-center">
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/online_inf_icon1.png" alt="">
                </div>
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/online_inf_icon2.png" alt="">
                </div>
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/online_inf_icon3.png" alt="">
                </div>
            </div>
            <div class="row pt-5 justify-content-center">
                <div class="col-12 text-center">
                    <h3>You can currently be requested by brands.</h3>
                </div>
            </div>
        </div>
        
        <div id="usr_offline"  @if((Auth::user()->status==1 && $req_count < 5)) style="display: none;" @endif>
            <div class="row pt-4 justify-content-center">
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/offline_inf_icon1.png" alt="">
                </div>
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/offline_inf_icon2.png" alt="">
                </div>
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/offline_inf_icon3.png" alt="">
                </div>
            </div>
            <div class="row pt-5 justify-content-center">
                <div class="col-12 text-center">
                    <h3>You cannot be requested until you switch back online.</h3>
                </div>
            </div>
        </div>
        
    </div>

    <!-- <div class="sahrePrice">
        @if(isset(Auth::user()->advertisingMethodPrice->sharecontent) && Auth::user()->advertisingMethodPrice->sharecontent ==1)
        <div class="myDiv {{Auth::user()->advertisingMethodPrice->sharecontent_media}}">
            <div class="sahrePriceHeading">Share content</div>
            <div class="checkone d-flex">
                <div class="iconbox"> 
                    <div class="socialCnt nmt ">  
                        <img src="{{ asset('/') }}/assets/front-end/images/col_icon_{{Auth::user()->advertisingMethodPrice->sharecontent_media}}.png" class="iconBlack" alt="">
                    </div>
                </div>
                <div class="inputs">
                    <div class="form-group pricing_top">
                        <label class="priceSpan">Price per post</label>
                        <div class="input-group">
                            <span class="input-group-text" id="basic-addon1">€</span>
                            <input type="text" name="sharecontent_price" placeholder="0" class="hidden-textbox form-control text-start ds" id="videoPrice" data-media="" data-followers="0" pattern="\d+\.?\d*" value="{{isset(Auth::user()->advertisingMethodPrice->sharecontent_price)?Auth::user()->advertisingMethodPrice->sharecontent_price:0}}" spellcheck="false" data-ms-editor="true">
                        </div>
                    </div>
                    
                </div>
            </div>
            <div class="text-center mt-mjh">
                <small>Depending on your followers, we suggest a price of you</small>
                <span class="suggest_price">{{isset($sharePrice)?round($sharePrice,2):0}}€</span>
            </div>
        </div>
         @endif


        @if(isset(Auth::user()->advertisingMethodPrice->video) && Auth::user()->advertisingMethodPrice->video ==1)
        <div class="myDiv {{Auth::user()->advertisingMethodPrice->video_media}}">
            <div class="sahrePriceHeading">Video on Demand</div>
            <div class="checkone d-flex">
                <div class="iconbox"> 
                    <div class="socialCnt nmt "> 
                        <img src="{{ asset('/') }}/assets/front-end/images/col_icon_{{Auth::user()->advertisingMethodPrice->video_media}}.png" class="iconBlack" alt="">
                    </div>
                </div>
                <div class="inputs">
                    <div class="form-group pricing_top">
                        <label class="priceSpan"><i class="fa-solid fa-circle-info "  data-bs-toggle="modal" data-bs-target="#exampleModal2"></i> Fixed price up to <span id="videoMinute">5 </span> Minutes</label>
                        <div class="input-group">
                            <span class="input-group-text" id="basic-addon1">€</span>
                            <input type="text" name="video_price" placeholder="0" class="hidden-textbox form-control text-start ds" id="video_price" data-media="" data-followers="0" pattern="\d+\.?\d*" value="{{isset(Auth::user()->advertisingMethodPrice->video_price)?Auth::user()->advertisingMethodPrice->video_price:0}}" spellcheck="false" data-ms-editor="true">
                        </div>
                        <small>Depending on your followers, we suggest a price of you</small>
                        <span class="suggest_price">{{isset($videoDemandPrice)?round($videoDemandPrice,2):0}}€</span>
                    </div>
                    <input type="hidden" name="video_minutes" id="totalVideoMinutes" value="5">
                </div>
                <div class="inputs">
                    <div class="form-group pricing_top">
                        <label class="priceSpan"><i class="fa-solid fa-circle-info " onclick="openModal()"  data-bs-toggle="modal" data-bs-target="#exampleModaladd" ></i> Additional variable price per <span id="addVideoMinute"> 5 </span> Minutes</label>
                        <div class="input-group">
                            <span class="input-group-text" id="basic-addon1">€</span>
                            <input type="text" name="video_price_additional" placeholder="0" class="hidden-textbox form-control text-start ds" id="video_price_additional" data-media="" data-followers="0" pattern="\d+\.?\d*" value="{{isset(Auth::user()->advertisingMethodPrice->video_price_additional)?Auth::user()->advertisingMethodPrice->video_price_additional:0}}" spellcheck="false" data-ms-editor="true">
                        </div>
                        <small>Depending on your followers, we suggest a price of you</small>
                        <span class="suggest_price">{{isset($addVideoDemandPrice)?round($addVideoDemandPrice,2):0}}€</span>
                    </div>
                </div>
            </div>
            
        </div>
         @endif

        @if(isset(Auth::user()->advertisingMethodPrice->livestream) && Auth::user()->advertisingMethodPrice->livestream ==1)
        <div class="myDiv {{Auth::user()->advertisingMethodPrice->livestream_media}}">
            <div class="sahrePriceHeading">Livestream</div>
            <div class="checkone d-flex">
                <div class="iconbox"> 
                    <div class="socialCnt nmt "> 
                        <img src="{{ asset('/') }}/assets/front-end/images/col_icon_{{Auth::user()->advertisingMethodPrice->livestream_media}}.png" class="iconBlack" alt="">
                    </div>
                </div>
                <div class="inputs">
                    <div class="form-group pricing_top">
                        <label class="priceSpan"><i class="fa-solid fa-circle-info " data-bs-toggle="modal" data-bs-target="#exampleModal2"></i> Fixed price up to <span id="videoMinute">5 </span> Minutes</label>
                        <div class="input-group">
                            <span class="input-group-text" id="basic-addon1">€</span>
                            <input type="text" name="livestream_price" placeholder="0" class="hidden-textbox form-control text-start ds" id="livestream_price" data-media="" data-followers="0" pattern="\d+\.?\d*" value="{{isset(Auth::user()->advertisingMethodPrice->livestream_price)?Auth::user()->advertisingMethodPrice->livestream_price:0}}" spellcheck="false" data-ms-editor="true">
                        </div>
                        <small>Depending on your followers, we suggest a price of you</small>
                        <span class="suggest_price">{{isset($livestreamPrice)?round($livestreamPrice,2):0}}€</span>
                    </div>
                    <input type="hidden" name="video_minutes" id="totalVideoMinutes" value="5">
                </div>
                <div class="inputs">
                    <div class="form-group pricing_top">
                        <label class="priceSpan"><i  onclick="openModal2()" class="fa-solid fa-circle-info " data-bs-toggle="modal" data-bs-target="#exampleModaladd"></i> Additional variable price per <span id="addVideoMinute"> 5 </span> Minutes</label>
                        <div class="input-group">
                            <span class="input-group-text" id="basic-addon1">€</span>
                            <input type="text" name="livestream_price_additional" placeholder="0" class="hidden-textbox form-control text-start ds" id="livestream_price_additional" data-media="" data-followers="0" pattern="\d+\.?\d*" value="{{isset(Auth::user()->advertisingMethodPrice->livestream_price_additional)?Auth::user()->advertisingMethodPrice->livestream_price_additional:0}}" spellcheck="false" data-ms-editor="true" >
                        </div>
                        <small>Depending on your followers, we suggest a price of you</small>
                        <span class="suggest_price">{{isset($addLivestreamPrice)?round($addLivestreamPrice,2):0}}€</span>
                    </div>
                </div>
            </div>
        </div>
         @endif

        <div class="iconbox alrtn">
            <i class="fa-solid fa-circle-info " data-bs-toggle="modal" data-bs-target="#exampleModal2"></i>
            <label class="custom-control custom-checkbox">
                <input type="checkbox" name="additional_price_check" id="customCheck" value="checkedValue" class="custom-control-input"  @if( Auth::user()->advertisingMethodPrice &&  ( Auth::user()->advertisingMethodPrice->additional_price_check ==1 ) ) Checked @endif >
                <span class="custom-control-indicator rdk"></span>Do you want to offer content exclusive rights for the brand?
            </label>
        </div>
        <label class="">Additional price for your content exclusive rights</label>
        <div class="checkone d-flex">
            <div class="inputs">
                <div class="form-group pricing_top">
                    <div class="input-group">
                        <span class="input-group-text" id="basic-addon1">€</span>
                        <input type="text" name="additional_price" class="hidden-textbox form-control text-start ds" id="additional_price" data-media="" data-followers="0" pattern="\d+\.?\d*" value="{{(isset(Auth::user()->advertisingMethodPrice->additional_price) && Auth::user()->advertisingMethodPrice->additional_price!= 0 )?Auth::user()->advertisingMethodPrice->additional_price:''}}" spellcheck="false" data-ms-editor="true"  {{ ( Auth::user()->advertisingMethodPrice &&  ( Auth::user()->advertisingMethodPrice->additional_price > 0) )? '' : 'readonly'}}  >
                    </div>
                </div>
            </div>
        </div>
    </div>
    @php $dialogues = DB::table('dialogues')->where('name', 'priceModaladd')->first(); @endphp
    <div class="modal fade" id="exampleModaladd" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="market_place_slider">
                        <div class="d-flex">
                            <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_facebook.png" class="iconBlack" alt=""><div class="placeSlidee_title">{{$dialogues->heading}}</div>
                        </div>
                        <div class="my-4 description">
                           {!!$dialogues->content!!}
                        </div>
                        <div class="place_slider">
                            <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css">
                            <div class="main">
                                <button type="button" class="btn-range" onclick="videoRange('minus')" data-dir="minus"><i class="fa-solid fa-angle-left"></i></button>
                                <div id="storlekslider" ></div>
                                <input type="hidden" name="storlek" id="storlek_testet" />
                                <input type="hidden" name="storlek_testet_price_additional" id="storlek_testet_price_additional" />
                                <button type="button" class="btn-range" onclick="videoRange('plus')" data-dir="plus"><i class="fa-solid fa-angle-right"></i></button>
                            </div>
                        </div>
                        <div class="pops">$<span class="price_value">0</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @php $dialogues = DB::table('dialogues')->where('name', 'priceModal')->first(); @endphp
    <div class="modal fade" id="exampleModal2" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="market_place_slider">
                        <div class="my-4 description">
                           {{$dialogues->heading}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> -->
</div>
{{-- <div class="bottom_links d-flex">
    <button type="button" id="Back_step4" class="et-submit  light-red-btn bin_links Back me-2">Back</button>
    @if(isset(Auth::user()->influencerDetail->publish ) && Auth::user()->influencerDetail->publish == 'Publish')
     @else
    
    <input type="button"  name="draft"  class="et-submit blueDarkBtn newpost draft me-3" value="Save as Draft" onclick="saveDraft(4)"  >
     @endif 

    <input type="hidden" name="publish" value="Publish"> 
    <!-- <input type="submit"  name="submit"  class="blueDarkBtn newpost" value="Publish"  onClick='return confirmReset()' > -->
    

    <button type="button" class="et-submit blueDarkBtn newpost float-none publish" 
        value="Publish">@if(isset(Auth::user()->influencerDetail->publish ) && Auth::user()->influencerDetail->publish == 'Publish') Update @else  Publish  @endif</button>

    <!-- <input type="hidden" class="et-submit publishButton" name="publish" value=""> -->
        @if(Auth::user()->status==1 && isset($influencer_detail->publish)) 
        <!-- <input type="submit" class="et-submit blueDarkBtn update newpost" name="update"  value="Update"   > -->
    @endif
    <button type="button" class="et-submit bin_links next ms-auto">Next</button>
</div>  --}}

<div class="step-nevigationbutton">
    <div class="nav-left back me-2" id="Back_step4">
        <img src="{{ asset('/') }}assets/front-end/images/icons/step-left.svg" class="" alt="">
    </div>
    {{-- @if($user->activate != '2') --}}
    <div class="nav-right next ms-auto" >
        <img src="{{ asset('/') }}assets/front-end/images/icons/step-right.svg" class="" alt="" style="background: #d5cbe9 !important;cursor:default;">
    </div>
    {{-- @endif --}}
</div>

<script type="text/javascript">

$('#customCheck').click(function(){
    if($(this).is(':checked') == true)
    {
        $('#additional_price').removeAttr('readonly');
        $('#additional_price').attr('required','');
    }else{
        $('#additional_price').attr('readonly', '');
        $('#additional_price').removeAttr('required');
        $('#additional_price').val('');
    }
});
$(document).ready(function(){
    if($('#customCheck').is(':checked') == true)
    {
        $('#additional_price').removeAttr('readonly');
        $('#additional_price').attr('required','');
    }else{
        $('#additional_price').attr('readonly', '');
        $('#additional_price').removeAttr('required');
        $('#additional_price').val('');
    }
})
$( "#storlekslider" ).slider({
    range: "max",
    min: 5,
    max: 120,
    step: 1,
    value: 5,
    slide: function( event, ui ) {
        //var value1 = $("#storlekslider").slider("value");  
        // $("#storlek_testet").val( ui.value );
        $(ui.value).val(minutes);
        $("#storlekslider").find(".ui-slider-handle").text(minutes +' minutes');  
    }
});

$("#storlekslider").find(".ui-slider-handle").text('5 minutes')
// $("#storlek_testet").keyup(function() {
//     $("#storlekslider").slider("value" , $(this).val());
//   var value1 = $("#storlek_testet").val();
//     $("#storlekslider").find(".ui-slider-handle").text(value1);
// });

$('#exampleModaladd').on('hidden.bs.modal', function () {
    $("#storlekslider").find(".ui-slider-handle").text("5 minutes")
});

function openModal(){
    console.log($("#video_price").val());
    console.log($("#video_price_additional").val());
    $('.price_value').html($("#video_price").val());
    $("#storlek_testet").val($("#video_price").val());
    $("#storlek_testet_price_additional").val($("#video_price_additional").val());
    $( "#storlekslider" ).slider({
        range: "max",
        min: 5,
        max: 120,
        step: 1,
        value: 5,
        slide: function( event, ui ) {
            //var value1 = $("#storlekslider").slider("value");  
            // $("#storlek_testet").val( ui.value );
            $(ui.value).val(5);
            $("#storlekslider").find(".ui-slider-handle").text('5 minutes');  
        }
    });
}

function openModal2(){
    console.log($("#livestream_price").val());
    console.log($("#livestream_price_additional").val());
    $('.price_value').html($("#livestream_price").val());
    $("#storlek_testet").val($("#livestream_price").val());
    $("#storlek_testet_price_additional").val($("#livestream_price_additional").val());
    $( "#storlekslider" ).slider({
        range: "max",
        min: 5,
        max: 120,
        step: 1,
        value: 5,
        slide: function( event, ui ) {
            //var value1 = $("#storlekslider").slider("value");  
            // $("#storlek_testet").val( ui.value );
            $(ui.value).val(5);
            $("#storlekslider").find(".ui-slider-handle").text('5 minutes');  
        }
    });
}



function videoRange(action){
    // $('#btnRange1').click(function() {
    var direction = action;
    var value =  $("#storlekslider").slider("value");
    var value1 =  $("#storlek_testet").val();
    var value2 =  $("#storlek_testet_price_additional").val();
    console.log('value'+value);
    console.log('value1'+value1);
    console.log('value2'+value2);
    if (direction == "plus") {
        if(parseInt(value) < 125){
            // console.log(parseInt(value1)+parseInt(value2));
            $('.price_value').html(parseInt(value1)+parseInt(value2));
            $("#storlek_testet").val(parseInt(value1)+parseInt(value2));
            $("#storlekslider").slider("value", value+5);
        }
    } else {
        if(value > 5){
            $('.price_value').html(parseInt(value1)-parseInt(value2));
            $("#storlek_testet").val(parseInt(value1)-parseInt(value2));
            $("#storlekslider").slider("value", parseInt(value)-5);
        }
    }

    var currentVal = $("#storlekslider").slider("value");
    // $("#storlek_testet").val(currentVal);
    $("#storlekslider").find(".ui-slider-handle").text(currentVal+" Minutes");
    // $("#videoMinute").text(currentVal);
    // $('#addVideoMinute').text(currentVal);
    // $('#totalVideoMinutes').val(currentVal);

    // });
}
// if($(this).is(':checked')){
//     $(this).closest(".publish-outer").find(".publish-last-div").removeClass("not-active");
//     $(this).closest(".publish-outer").find(".am-selected-media").removeClass("not-active");
// }else{
//     $(this).closest(".publish-outer").find(".publish-last-div").addClass("not-active");
//     $(this).closest(".publish-outer").find(".am-selected-media").addClass("not-active");
// }
// $("input[name=status]").on("change",function(){
//     if($(this).is(':checked')){
//         $(this).closest(".publish-outer").find(".publish-last-div").removeClass("not-active");
//         $(this).closest(".publish-outer").find(".am-selected-media").removeClass("not-active");
//     }else{
//         $(this).closest(".publish-outer").find(".publish-last-div").addClass("not-active");
//         $(this).closest(".publish-outer").find(".am-selected-media").addClass("not-active");
//     }
// })



</script>