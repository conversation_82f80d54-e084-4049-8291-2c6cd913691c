<!--modal taskModal form -->
<div class="modal fade influncer wewPopup" id="taskModal{{ $influencerDataItem->compaign_id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="taskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body pb-5">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                        alt="">
                </button>
                <div class="wizardHeading">My Tasks</div>
                @php $tasks = $influencerDataItem->tasks ; @endphp
                @if (isset($tasks))
                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                            <div class="inside-table-row">
                                <div class="order-titles">
                                    {{ $task->taskDetail->task }}
                                </div>
                                <div class="order-content">
                                    {{ $task->value }}
                                </div>
                            </div>
                        @endif
                    @endforeach

                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'Link')
                            <div class="inside-table-row">
                                <div class="order-titles">
                                    {{ $task->taskDetail->task }}
                                </div>
                                <div class="order-content">
                                    <div class="order-link">
                                        <div class="link"
                                            id="myInput{{ $task->id }}">
                                            {{ $task->value }}</div>
                                        <div class="copy-link">
                                            <a class="copy_text" id="jjhu"
                                                data-toggle="tooltip"
                                                title="Copy to Clipboard"
                                                href="{{ $task->value }}">
                                                <span class="">COPY</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endforeach


                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                            <div class="inside-table-row">
                                <div class="order-titles">
                                    {{ $task->taskDetail->task }}
                                </div>
                                <div class="order-content">

                                    @if ($influencerDataItem->post_content_type == 'video')
                                        <video controls>
                                            <source
                                                src="{{ url('storage/app') . '/' . $task->value }}"
                                                type="video/mp4">
                                        </video>
                                    @else
                                        <a href="{{ url('storage/app') . '/' . $task->value }}"
                                            download>
                                            <img src="{{ url('storage/app') . '/' . $task->value }}"
                                                width="40">
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endif
                    @endforeach

                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                            <div class="inside-table-row">
                                <div class="order-titles">
                                    {{ $task->taskDetail->task }}
                                </div>
                                <div class="order-content">
                                    <?php $tags = explode(',', $task->value); ?>
                                    @foreach ($tags as $tag)
                                        @if ($tag)
                                            <div class="order-hash-tag">
                                                <img src="{{ asset('/') }}/assets/front-end/images/icon-hash.png"
                                                    alt="">
                                                {{ $tag }}
                                            </div>
                                        @endif
                                    @endforeach

                                </div>
                            </div>
                        @endif
                    @endforeach

                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'Info')
                            <div class="inside-table-row">
                                <div class="order-titles">
                                    {{ $task->taskDetail->task }}
                                </div>
                            </div>
                        @endif
                    @endforeach
                @endif

                <div class="wizardForm">
                </div>
            </div>
        </div>
    </div>
</div>
<!--end modal taskModal form -->
<!-- Show Detail-->
<div class="modal fade influncer wewPopup request-popup"
    id="requestForm{{ $influencerDataItem->id }}" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestFormLabel"
    aria-hidden="true">
    <div class="modal-dialog default-width modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close">
                    <img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                        alt="">
                </button>
                <div class="popup-title">{{ $influencerDataItem->compaign_title }}</div>
                <div class="popup-title-id">Campaign ID: {{ $influencerDataItem->compaign_id }}</div>
                <form method="post" id="requestFormSubmit{{ $influencerDataItem->id }}"
                    action="{{ url('/request-form') }}" data-parsley-validate>
                    @csrf
                    <input type="hidden" name="influencer_request_detail_id"
                        id="influencer_request_detail_id"
                        value="{{ isset($influencerDataItem->id) ? $influencerDataItem->id : '' }}">
                    <ul class="nav nav-tabs ordertab" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active"
                                id="general-information-tab{{ $influencerDataItem->compaign_id }}"
                                data-bs-toggle="tab"
                                data-bs-target="#general-information{{ $influencerDataItem->compaign_id }}"
                                type="button" role="tab"
                                aria-controls="general-information{{ $influencerDataItem->compaign_id }}"
                                aria-selected="true">General Information
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link"
                                id="order-detail-tab{{ $influencerDataItem->compaign_id }}"
                                data-bs-toggle="tab"
                                data-bs-target="#order-detail{{ $influencerDataItem->compaign_id }}"
                                type="button" role="tab"
                                aria-controls="order-detail{{ $influencerDataItem->compaign_id }}"
                                aria-selected="false">My Tasks
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active"
                            id="general-information{{ $influencerDataItem->compaign_id }}"
                            role="tabpanel"
                            aria-labelledby="general-information-tab{{ $influencerDataItem->compaign_id }}">

                            <div class="inside-table request-content">
                                <div class="inside-table-row">
                                    <span class="type-label">Company Name</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-user-black.svg"
                                            class="" alt=""></span>
                                    @if (isset($influencerDataItem->user->company_name))
                                        <span
                                            class="type-content">{{ $influencerDataItem->user->company_name }}</span>
                                    @endif
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Request date</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-calender-black.svg"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ date('d.m.Y', strtotime($influencerDataItem->created_at)) }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">You get</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ number_format($influencerDataItem->current_price, 2) }}
                                        €</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Social Media</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-cb-{{ $influencerDataItem->media }}.svg"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ ucfirst($influencerDataItem->media) }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Brand name</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-brandname-black.svg"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ $influencerDataItem->name }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Campaign type</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-boostme-black.svg"
                                            class="" alt=""></span>
                                    <span class="type-content">{{ $influencerDataItem->post_type }}
                                    </span>
                                </div>
                                @if (isset($influencerDataItem->category))
                                    <div class="inside-table-row">
                                        <span class="type-label">Category</span>
                                        <span class="type-image"><img
                                                src="{{ asset('/') }}/assets/front-end/images/icons/icon-category-black.svg"
                                                class="" alt=""></span>
                                        <span
                                            class="type-content">{{ $influencerDataItem->category->name }}</span>
                                    </div>
                                @endif
                                <div class="inside-table-row">
                                    <span class="type-label">Results in</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-clock-black.svg"
                                            class="" alt=""></span>
                                    <span class="type-content">@php
                                        $time =
                                            isset($influencerDataItem->request_time_accept) &&
                                            $influencerDataItem->request_time_accept == 1
                                                ? $influencerDataItem->request_time + $influencerDataItem->time
                                                : $influencerDataItem->time;

                                        $created_date = date(
                                            'Y-m-d H:i:s',
                                            strtotime($influencerDataItem->created_at),
                                        );
                                        $updated_date = date(
                                            'Y-m-d H:i:s',
                                            strtotime($influencerDataItem->updated_at),
                                        );
                                        $campaignDate = date(
                                            'Y-m-d H:i:s',
                                            strtotime(
                                                $created_date .
                                                    ' + ' .
                                                    $campaignRequestTime->request_time .
                                                    ' days',
                                            ),
                                        );
                                        $date = date('Y-m-d H:i:s');
                                        $seconds =
                                            strtotime($campaignDate) -
                                            strtotime($date);

                                        $days = floor($seconds / 86400);
                                        if ($days < 3 && $days >= 0) {
                                            $hours = floor(
                                                ($seconds - $days * 86400) / 3600,
                                            );

                                            $minutes = floor(
                                                ($seconds -
                                                    $days * 86400 -
                                                    $hours * 3600) /
                                                    60,
                                            );

                                            $seconds = floor(
                                                $seconds -
                                                    $days * 86400 -
                                                    $hours * 3600 -
                                                    $minutes * 60,
                                            );
                                        }
                                    @endphp
                                        {{-- <div class="timing" id="timer1{{ $influencerDataItem->compaign_id }}"></div> --}}
                                        <div class="timing">10 days</div>
                                </div>
                            </div>

                        </div>
                        <div class="tab-pane fade"
                            id="order-detail{{ $influencerDataItem->compaign_id }}" role="tabpanel"
                            aria-labelledby="order-detail-tab{{ $influencerDataItem->compaign_id }}">
                            <div class="request-content-data icon-before">
                                @php $tasks = $influencerDataItem->tasks ; @endphp
                                @if (isset($tasks))
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    {{ $task->value }}
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Link')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <div class="order-link">
                                                        <div class="link"
                                                            id="myInput{{ $task->id }}">
                                                            {{ $task->value }}</div>
                                                        <div class="copy-link">
                                                            <a class="copy_text"
                                                                id="jjhu"
                                                                data-toggle="tooltip"
                                                                title="Copy to Clipboard"
                                                                href="{{ $task->value }}">
                                                                <span
                                                                    class="">COPY</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach


                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <a class="table-btn"
                                                        href="{{ asset('storage/' . $task->value) }}"
                                                        download
                                                        style="color: black !important;width:186px !important;height:40px;box-shadow:none !important;">
                                                        Download
                                                    </a>
                                                    @if ($influencerDataItem->post_content_type == 'video')
                                                        <img src="{{ url('/assets/front-end/icons/video_placeholder.png') }}"
                                                            width="40">
                                                    @else
                                                        <img src="{{ url('/assets/front-end/icons/image_placholder.png') }}"
                                                            width="40">
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach

                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <?php $tags = explode(',', $task->value); ?>
                                                    @foreach ($tags as $tag)
                                                        @if ($tag)
                                                            <div
                                                                class="order-hash-tag">
                                                                <img src="{{ asset('/') }}/assets/front-end/images/icon-hash.png"
                                                                    alt="">
                                                                {{ $tag }}
                                                            </div>
                                                        @endif
                                                    @endforeach

                                                </div>
                                            </div>
                                        @endif
                                    @endforeach

                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Info')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- end -->

<!--cancel popup start-->
<div class="modal fade" id="cancelRequest{{ $influencerDataItem->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="cancelRequest{{ $influencerDataItem->id }}Label" target="popup"
    data-bs-toggle="modal" data-bs-target="#cancelRequest{{ $influencerDataItem->id }}Label"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-body">

                <div class="text-center errorBox">
                    <div class="text-center chkImage"><img
                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-high-demand.svg"
                            alt=""></div>
                    <div class="manreq-title"
                        style="font-size: 16px;line-height: normal;">Are you certain
                        about canceling the campaign?<br><br> Please be aware that
                        canceling means you won't
                        receive payment, and it will also impact your ranking points
                        negatively.</div>
                    <!-- Selected users //-->
                    <div class="widthBtnPopup d-flex">
                        <input class="et-submit accept mx-1 ds" type="button"
                            value="Yes"
                            onclick="requestCancelbutton('{{ $influencerDataItem->id }}')">
                        <button class="et-submit red-btn reject mx-1 ds"
                            type="button" data-bs-dismiss="modal"
                            aria-label="Close">No</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--cancel popup end-->

<!--start modal for request Submit -->
<div class="modal fade confirm-content influncer"
    id="contact-support-campaign{{ $influencerDataItem->id }}" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="requestSubmit{{ $influencerDataItem->id }}Label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                        alt=""></button>
                <div class="wizardHeading">Contact support</div>
                <div class="contact-support">
                    <div class="text-center wizardHeading-subheading">Please describe your
                        problem and
                        upload screenshots of the insights from the content you wanted to submit
                    </div>
                    <form action="{{ url('/contact-support') }}" method="Post"
                        enctype="multipart/form-data" data-parsley-validate>
                        @csrf
                        <input type="hidden" name="influencer_request_detail_id"
                            value="{{ $influencerDataItem->id }}">

                        <div class="form-group">
                            <label for="" class="form-label"></label>
                            <textarea class="form-control" name="comment" id="comment" rows="3"
                                placeholder="Please describe your problem" required data-parsley-required-message="Please enter problem."></textarea>
                        </div>
                        <div class="form-group uploadFile">
                            <div class="custom-file-picker">
                                <div class="picture-container form-group">
                                    <div class="picture">
                                        <span class="icon" id="icon">
                                            <div class="smaltext">Browse</div>
                                            <div class="bigtext">Or Drag and Drop to Upload
                                            </div>
                                        </span>
                                        <input type="file" class="wizard-file"
                                            name="file"
                                            id="a8755cf0-f4d1-6376-ee21-a6defd1e7c08">
                                        <svg version="1.1" xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
                                            y="0px" viewBox="0 0 37 37" xml:space="preserve">
                                            <path class="circ path"
                                                style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;"
                                                d="M30.5,6.5L30.5,6.5c6.6,6.6,6.6,17.4,0,24l0,0c-6.6,6.6-17.4,6.6-24,0l0,0c-6.6-6.6-6.6-17.4,0-24l0,0C13.1-0.2,23.9-0.2,30.5,6.5z">
                                            </path>
                                            <polyline class="tick path"
                                                style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;"
                                                points="11.6,20 15.9,24.2 26.4,13.8 ">
                                            </polyline>
                                        </svg>
                                        <div class="popover-container text-center show-file">
                                            <p data-toggle="popover"
                                                data-id="a8755cf0-f4d1-6376-ee21-a6defd1e7c08"
                                                class="btn-popover" data-original-title=""
                                                title="">
                                                <span class="file-total-viewer">0</span> Files
                                                Selected
                                                <br /><input type="button" value="view"
                                                    href="javascript:void(0)"
                                                    class="btn btn-success btn-xs btn-file-view">
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="extra-time-content-bs">
                            <input type="submit" class="et-submit ds" name="confirm" value="Submit">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end  modal for request time -->

<div class="modal fade confirm-content influncer" id="pause_campaign_modal" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestSubmitLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="">
                </button>
                <div class="wizardHeading">PAUSE CAMPAIGN</div>
                <div class="pause-campaign-text">
                    <h5 align="center" style="padding-bottom: 15px">Before pausing the campaign,
                        please
                        check if you have already completed the following points:</h5>
                    <ol>
                        <li><b>Private account</b> - First check whether the privacy settings of your
                            social
                            media account have
                            been set to 'private'. Content from accounts that are set to private cannot
                            be
                            viewed publicly. To check this, go to
                            your social media profile settings and look for the privacy or data
                            protection
                            section. Make sure that your account
                            is set to public.</li>
                        <li><b>Reconnect your Social Media Account</b> - Go to 'My Service' and
                            disconnect
                            from your social media account.
                            After you have disconnected, reconnect your account and then try to submit
                            your
                            content again.</li>
                        <li><b>Use the private/incognito mode of your browser</b> - This mode prevents
                            your
                            browser from saving temporary files,
                            such as cookies and cache, which can sometimes affect the functionality of
                            API
                            connections. By using incognito
                            mode, you ensure that each session is free of previous session data, which
                            can
                            sometimes be helpful.</li>
                    </ol>

                    <p>If none of these solutions help, we will get in touch with you. Please note that
                        we
                        may manually check whether
                        you have posted the content at the right time.</p>

                    <div align="center">
                        <input type="checkbox" id="pause_check_btn" style="transform: scale(1.5);">
                        &nbsp;&nbsp;<span>Yes, I have already tried the solutions mentioned
                            above</span><br>
                        <div class="button-container" style="margin-bottom: 25px;">
                            <a href="#" class="table-btn red-btn ds"
                                data-toggle="pause_campaign_next_btn"
                                style="width: 165px !important; margin: 20px 0 0px; display: block;padding:4px;">Next</a>
                            <span id="pause_campaign_error"
                                style="color: red; display: block;margin: 0px 0 30px;display:none;">Please
                                confirm that you have tried the solutions</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade confirm-content influncer" id="pause_campaign_submit_modal"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="requestSubmitLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                        alt=""></button>
                <div class="wizardHeading">DETAILS</div>
                <div class="contact-support">
                    <form action="{{ url('/pause-campaign') }}" method="Post"
                        enctype="multipart/form-data" data-parsley-validate>
                        @csrf
                        <input type="hidden" name="rowId" id="camp_rowId">

                        <div class="form-group">
                            <label for="" class="form-label">Please Describe your
                                problem</label>
                            <textarea class="form-control" name="description" id="comment" rows="2"
                                placeholder="Please describe your problem" required data-parsley-required-message="Please enter problem."
                                style="height: 200px !important;"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="social_media_link" class="form-label">The link of your social
                                media post</label>
                            <input type="text" class="form-control" name="social_media_link"
                                id="social_media_link" placeholder="" required
                                data-parsley-required-message="Please enter link.">
                        </div>
                        <div class="form-group">
                            <label for="phon_no" class="form-label">Please provide your phone number
                                if
                                additional details are required.</label>
                            <input type="text" class="form-control" name="phon_no" id="phon_no"
                                placeholder="" required
                                data-parsley-required-message="Enter phone number.">
                        </div>
                        <div class="extra-time-content-bs">
                            <input type="submit" class="table-btn red-btn ds" name="confirm"
                                value="Pause Campaign" style="width: 215px !important;">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!--start modal for request time -->
<div class="modal fade wewPopup requesttime influncer" id="requestTime" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestTimeLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                        alt=""></button>
                <div class="wizardHeading">Request more time</div>
                <div class="sortNN">
                    <div class="extra-time-content">
                        <p>You will get additional of <img
                                src="{{ asset('/') }}/assets/front-end/images/icon-seven.png"
                                alt=""> days on top of your current time</p>
                        <div class="cont-suy">
                            <img src="{{ asset('/') }}/assets/front-end/images/icon-attention.png"
                                alt=""> The Brand can reject your request
                        </div>
                        <form method="post" id="requestOntr"
                            action="{{ url('/request-more-time') }}" data-parsley-validate>
                            @csrf
                            <input type="hidden" name="influencer_request_accept_id"
                                id="request_influencer_request_id">
                            <input type="hidden" name="request_time" id="request_time"
                                value="7">

                            <div class="extra-time-content-bs">
                                <input type="submit" class="et-submit showLoader" name="confirm"
                                    value="Submit">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end  modal for request time -->
<div class="modal fade complaint-confirm-popup influncer"
    id="thankYouContact"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
    tabindex="-1"
    aria-labelledby="reviewRatingPopupLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <a href="{{ url('active-campaigns') }}"> <button type="button" class="btn-close"
                        data-bs-dismiss="modal" aria-label="Close"><img
                            src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                            alt=""></button></a>

                <div class="complaint-confirm text-center">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                        alt="" class="complaint-confirm-image">
                    <p class="thank-title1">
                        Thank you for contacting us!
                    </p>
                    <p class="thank-title2">
                        We will be in touch with you soon
                    </p>
                    <a href="{{ url('active-campaigns') }}" class="et-submit mx-4 complant-btn"
                        aria-label="Close">Confirm</a>
                </div>

            </div>
        </div>
    </div>
</div>