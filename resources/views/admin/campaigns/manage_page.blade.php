@php  $count=0; @endphp
@if (isset($result))
    @foreach ($result as $row)
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading{{ $row->compaign_id }}"
                @if ($row->read_status != '' && $row->read_at == '') onclick="openActive('{{ $row->compaign_id }}')" @endif>
                <button class="accordion-button" type="button">

                    <table border="0">
                        <tbody>
                            <tr>
                                <td class="taskName" colspan="6">
                                    <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{ $row->media }}.svg"
                                        alt="" class="standard-icon"><span target="popup" data-bs-toggle="modal"
                                        data-bs-target="#taskModal{{ $row->compaign_id }}"> {{ $row->compaign_title }}
                                        {{ $row->compaign_id }} </span>
                                </td>

                                <td class="camp-button dropdown-button vertical-bottom" rowspan="2">

                                    @php
                                        $influencerRequestDetails = App\Models\InfluencerRequestDetail::where(
                                            'compaign_id',
                                            $row->compaign_id,
                                        )->get();
                                        $finish_timer = 0;
                                        $firstInfluencerRequest = $influencerRequestDetails[0];
                                        $time =
                                            isset($firstInfluencerRequest->influencer_request_accepts->request_time_accept) &&
                                            $firstInfluencerRequest->influencer_request_accepts->request_time_accept == 1
                                                ? $firstInfluencerRequest->influencer_request_accepts->request_time + $firstInfluencerRequest->time
                                                : $firstInfluencerRequest->time;
                                    @endphp



                                    @foreach ($influencerRequestDetails as $influencerRequestDetail)
                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                        @if (empty($influencerRequestDetail->influencerdetails))
                                            @php continue; @endphp
                                        @endif
                                        @if ($influencerRequestDetail->social_post_id != null || $influencerRequestDetail->refund_reason != null)
                                            @php $finish_timer = $finish_timer + 1; @endphp
                                        @endif
                                    @endforeach


                                    <input type="hidden" id="datalist{{ $row->compaign_id }}"
                                        value="{{ $time }}">


                                </td>
                                <td class="camp-button dropdown-button collapsed" data-bs-toggle="collapse"
                                    data-bs-target="#collapse{{ $row->compaign_id }}" aria-expanded="false"
                                    aria-controls="collapse{{ $row->compaign_id }}" rowspan="2">
                                    @if (
                                        $row->refund_reason != 'Cancelled' &&
                                            ($row->status == '0' ||
                                                $row->invoice_id == null ||
                                                (isset($row->influencer_request_accepts->request_time) &&
                                                    ($row->influencer_request_accepts->request_time > 0 &&
                                                        $row->influencer_request_accepts->request_time_accept == null)) ||
                                                ($row->total_amount > 0 &&
                                                    isset($row->social_posts) &&
                                                    $row->social_posts != null &&
                                                    !isset($row->influencer_request_accepts->rating_reviews) &&
                                                    !isset($row->influencer_request_accepts->complaints)) ||
                                                isset($row->influencer_request_accepts->rating_reviews) ||
                                                (isset($row->influencer_request_accepts->complaints) &&
                                                    $row->influencer_request_accepts->complaints->status == 'Inprogress')))
                                        <img src="{{ asset('/') }}/assets/front-end/images/dropdown-image.png"
                                            alt="">
                                    @else
                                        <img src="{{ asset('/') }}/assets/front-end/images/dropdown-image.png"
                                            class="colorblk" alt="">
                                    @endif
                                    {{-- <img src="{{ asset('/') }}/assets/front-end/images/dropdown-image.png  " alt="" > --}}
                                </td>
                            </tr>
                            <tr>
                                <td class="firDaat">
                                    <span class="handelpletform">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{ $row->media }}.svg"
                                            alt="">
                                        <span class="story-type">{{ $row->post_type }}</span>
                                    </span>
                                </td>
                                <td class="soclDetail">
                                    <div class="d-flex align-items-center">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/req-clipboard.svg"
                                            alt="">
                                        <span class="sertp">{{ $row->advertising }}</span>
                                    </div>
                                </td>
                                <td class="custDetail">
                                    <div class="d-flex align-items-center">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/req-user.svg"
                                            alt="">
                                        <div class="influncercount" target="popup" data-bs-toggle="modal"
                                            data-bs-target="#showInfluencers13">
                                            {{ $row->total_influencer_count }} Influencer
                                        </div>
                                    </div>
                                </td>
                                <td class="show-pricing">
                                    <div class="d-flex align-items-center">
                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                            alt="">
                                        <div class="soclPrice">
                                            {{ number_format($row->total_amount, 2) }} €
                                        </div>
                                    </div>
                                </td>
                                <td class="camp-button">
                                    <div class="">
                                        <a href="javascript:void(0);" class="table-btn nobg-btn mx-3" target="popup"
                                            data-bs-toggle="modal"
                                            data-bs-target="#requestForm{{ $row->id }}">Campaign Details</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </button>
            </h2>

            <div id="collapse{{ $row->compaign_id }}" class="accordion-collapse collapse "
                aria-labelledby="heading{{ $row->compaign_id }}" data-bs-parent="#accordionExample">
                <div class="accordion-body">
                    @php
                        $finish = 0;
                        $influencerRequestDetails = App\Models\InfluencerRequestDetail::where('compaign_id', $row->compaign_id)->get();
                    @endphp
                    @foreach ($influencerRequestDetails as $influencerRequestDetail)
                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                        @if (empty($influencerRequestDetail->influencerdetails))
                            @php continue; @endphp
                        @endif
                        @if ($influencerRequestDetail->status == '0' || $influencerRequestDetail->invoice_id == null)
                            @php $finish = $finish + 1; @endphp
                        @else
                            @if (isset($influencerRequestDetail->influencer_request_accepts->request_time) &&
                                    ($influencerRequestDetail->influencer_request_accepts->request_time > 0 &&
                                        $influencerRequestDetail->influencer_request_accepts->request_time_accept == null))
                            @elseif(
                                $influencerRequestDetail->total_amount > 0 &&
                                    isset($influencerRequestDetail->social_posts) &&
                                    $influencerRequestDetail->social_posts != null &&
                                    !isset($influencerRequestDetail->influencer_request_accepts->rating_reviews) &&
                                    !isset($influencerRequestDetail->influencer_request_accepts->complaints))

                            @elseif(isset($influencerRequestDetail->influencer_request_accepts->rating_reviews))
                                @php $finish = $finish + 1; @endphp
                            @elseif(isset($influencerRequestDetail->influencer_request_accepts->complaints))
                                @php $finish = $finish + 1; @endphp
                            @elseif($influencerRequestDetail->refund_reason != '')
                                @php $finish = $finish + 1; @endphp
                            @endif
                        @endif
                    @endforeach
                    @php
                        $finish = 0;
                        $influencerRequestDetails = App\Models\InfluencerRequestDetail::where('compaign_id', $row->compaign_id)->get();
                    @endphp
                    <div class="data-table cac">
                        <ul class="nav nav-tabs filtertab" id="myTab{{ $row->id }}" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link all active" id="all-tab{{ $row->id }}"
                                    data-bs-toggle="tab" data-bs-target="#all{{ $row->id }}" type="button"
                                    role="tab" aria-controls="all{{ $row->id }}"
                                    aria-selected="true">All</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link action" id="action-tab{{ $row->id }}" data-bs-toggle="tab"
                                    data-bs-target="#action{{ $row->id }}" type="button" role="tab"
                                    aria-controls="action{{ $row->id }}" aria-selected="false">Action</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link inprogress" id="inprogress-tab{{ $row->id }}"
                                    data-bs-toggle="tab" data-bs-target="#inprogress{{ $row->id }}"
                                    type="button" role="tab" aria-controls="inprogress{{ $row->id }}"
                                    aria-selected="false">In progress</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link complete" id="completed-tab{{ $row->id }}"
                                    data-bs-toggle="tab" data-bs-target="#completed{{ $row->id }}"
                                    type="button" role="tab" aria-controls="completed{{ $row->id }}"
                                    aria-selected="false">Completed</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link cancelled" id="cancelled-tab{{ $row->id }}"
                                    data-bs-toggle="tab" data-bs-target="#cancelled{{ $row->id }}"
                                    type="button" role="tab" aria-controls="cancelled{{ $row->id }}"
                                    aria-selected="false">Cancelled</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="all{{ $row->id }}" role="tabpanel"
                                aria-labelledby="all-tab{{ $row->id }}">
                                <table>
                                    @foreach ($influencerRequestDetails as $influencerRequestDetail)
                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                        @if (empty($influencerRequestDetail->influencerdetails))
                                            @php continue; @endphp
                                        @endif
                                        @php
                                            $count1 = 0;
                                            $time =
                                                isset($influencerRequestDetail->influencer_request_accepts->request_time_accept) &&
                                                $influencerRequestDetail->influencer_request_accepts->request_time_accept == 1
                                                    ? $influencerRequestDetail->influencer_request_accepts->request_time + $influencerRequestDetail->time
                                                    : $influencerRequestDetail->time;

                                            $created_date = date('Y-m-d H:i:s', strtotime($influencerRequestDetail->created_at));
                                            $updated_date = date('Y-m-d H:i:s', strtotime($influencerRequestDetail->updated_at));
                                            $campaignDate = date(
                                                'Y-m-d H:i:s',
                                                strtotime($updated_date . ' + ' . $time . ' days'),
                                            );
                                            $date = date('Y-m-d H:i:s');
                                            $seconds = strtotime($campaignDate) - strtotime($date);
                                            $days = floor($seconds / 86400);
                                        @endphp
                                        @if (
                                            $influencerRequestDetail->review != 1 &&
                                                $influencerRequestDetail->is_complained != 1 &&
                                                $influencerRequestDetail->invoice_id != null &&
                                                $influencerRequestDetail->refund_reason == null &&
                                                isset($influencerRequestDetail->social_posts) &&
                                                $influencerRequestDetail->social_posts != null)
                                            @php $count1 =1; @endphp
                                            <tr class="accordian-table action">
                                            @elseif(
                                                ($days >= 0 && ($influencerRequestDetail->refund_reason != 'Cancelled' && !isset($influencerRequestDetail->social_posts) && $influencerRequestDetail->review != '1')) ||
                                                    ($influencerRequestDetail->is_complained == 1 && $influencerRequestDetail->refund_reason != 'Complaint Confirmed' && $influencerRequestDetail->review != '1'))
                                            <tr class="accordian-table inprogress">
                                            @elseif(
                                                $influencerRequestDetail->finish == 1 ||
                                                    $influencerRequestDetail->review == 1 ||
                                                    (isset($influencerRequestDetail->influencer_request_accepts->complaints) &&
                                                        $influencerRequestDetail->influencer_request_accepts->complaints->status == 'Cancelled'))
                                                @php $count1 =0; @endphp
                                            <tr class="accordian-table completed">
                                            @elseif(
                                                $influencerRequestDetail->refund_reason == 'Cancelled' ||
                                                    $influencerRequestDetail->refund_reason == 'Complaint Confirmed' ||
                                                    (isset($influencerRequestDetail->influencer_request_accepts->complaints) &&
                                                        $influencerRequestDetail->influencer_request_accepts->complaints->status == 'Confirmed'))
                                                @php $count1 =0; @endphp
                                            <tr class="accordian-table cancelled">
                                        @endif

                                        @if (
                                            $influencerRequestDetail->review != 1 &&
                                                $influencerRequestDetail->is_complained != 1 &&
                                                $influencerRequestDetail->invoice_id != null &&
                                                $influencerRequestDetail->refund_reason == null &&
                                                isset($influencerRequestDetail->social_posts) &&
                                                $influencerRequestDetail->social_posts != null)
                                            @php $count1 =1; @endphp

                                            <td>
                                                <img src="{{ asset('/') }}/assets/front-end/images/data-info.png"
                                                    alt="">
                                            </td>
                                        @elseif(
                                            ($days >= 0 && ($influencerRequestDetail->refund_reason != 'Cancelled' && !isset($influencerRequestDetail->social_posts) && $influencerRequestDetail->review != '1')) ||
                                                ($influencerRequestDetail->is_complained == 1 && $influencerRequestDetail->refund_reason != 'Complaint Confirmed' && $influencerRequestDetail->review != '1'))
                                            <td>
                                                <img src="{{ asset('/') }}/assets/front-end/images/data-time.svg"
                                                    alt="">
                                            </td>
                                        @elseif(
                                            $influencerRequestDetail->finish == 1 ||
                                                $influencerRequestDetail->review == 1 ||
                                                (isset($influencerRequestDetail->influencer_request_accepts->complaints) &&
                                                    $influencerRequestDetail->influencer_request_accepts->complaints->status == 'Cancelled'))
                                            @php $count1 =0; @endphp
                                            <td>
                                                <img src="{{ asset('/') }}/assets/front-end/images/data-check.png"
                                                    alt="">
                                            </td>
                                        @elseif(
                                            $influencerRequestDetail->refund_reason == 'Cancelled' ||
                                                $influencerRequestDetail->refund_reason == 'Complaint Confirmed' ||
                                                (isset($influencerRequestDetail->influencer_request_accepts->complaints) &&
                                                    $influencerRequestDetail->influencer_request_accepts->complaints->status == 'Confirmed'))
                                            @php $count1 =0; @endphp
                                            <td>
                                                <img src="{{ asset('/') }}/assets/front-end/images/data-close.png"
                                                    alt="">
                                            </td>
                                        @endif

                                        @php
                                            $socialLink = App\Models\SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user->id)
                                                ->where('media', $row->media)
                                                ->first();
                                        @endphp
                                        @if ($socialLink != '')
                                            <td>
                                                <div class="data-set-user d-flex align-items-center ">
                                                    <img src="{{ asset('/') }}/assets/front-end/images/data-user.png"
                                                        alt="">
                                                    <span><a target="popup" data-bs-toggle="modal"
                                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                            {{ isset($socialLink->name) ? $socialLink->name : '' }}</a></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="data-set-foll d-flex align-items-center ">
                                                    <img src="{{ asset('/') }}/assets/front-end/images/data-users.png"
                                                        alt="">
                                                    {{ @$socialLink->followers }} Follower
                                                </div>
                                            </td>
                                        @endif
                                        <td>
                                            <div class="data-set-price d-flex align-items-center ">
                                                <img src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                                    alt="">
                                                <?php
                                                $newLivetreamPrice = 0;
                                                $addNewLivetreamPrice = 0;

                                                $user = App\Models\User::find($influencerRequestDetail->influencerdetails->user->id);

                                                $fieldName = $influencerRequestDetail->advertising . '_price';
                                                if ($user->advertisingMethodPrice != null) {
                                                    $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                }
                                                ?>
                                                {{ number_format($influencerRequestDetail->current_price, 2) }} €
                                            </div>

                                        </td>
                                        <td class="text-center">
                                            @if ($row->finish == '1')
                                                Finished
                                            @elseif(@$row->status == 'Cancelled')
                                                Cancelled
                                            @elseif(@$row->status == '2')
                                                Payment Submitted
                                            @else
                                                <input type="hidden" name="ids[]" value="{{ @$row->id }}">
                                                <select name="finish[]">
                                                    <option value="">Select</option>
                                                    <option {{ @$row->status == 'Cancelled' ? 'selected' : '' }}>Cancelled
                                                    </option>
                                                </select>
                                            @endif
                                        </td>
                                        </tr>
                                    @endforeach
                                    @if (count($influencerRequestDetails) == 0)
                                        <tr>
                                            <td class="text-center">Empty</td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                            <div class="tab-pane fade" id="action{{ $row->id }}" role="tabpanel"
                                aria-labelledby="action-tab{{ $row->id }}">
                                <table>
                                    @php $actionCount = 0; @endphp
                                    @foreach ($influencerRequestDetails as $influencerRequestDetail)
                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                        @if (empty($influencerRequestDetail->influencerdetails))
                                            @php continue; @endphp
                                        @endif
                                        @if (
                                            $influencerRequestDetail->review != 1 &&
                                                $influencerRequestDetail->is_complained != 1 &&
                                                $influencerRequestDetail->invoice_id != null &&
                                                $influencerRequestDetail->refund_reason == null &&
                                                isset($influencerRequestDetail->social_posts) &&
                                                $influencerRequestDetail->social_posts != null)
                                            @php $actionCount++; @endphp

                                            <tr class="accordian-table action">

                                                <td>
                                                    <img src="{{ asset('/') }}/assets/front-end/images/data-info.png"
                                                        alt="">
                                                </td>
                                                @php
                                                    $socialLink = App\Models\SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user->id)
                                                        ->where('media', $row->media)
                                                        ->first();
                                                @endphp
                                                @if ($socialLink != '')
                                                    <td>
                                                        <div class="data-set-user d-flex align-items-center ">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/data-user.png"
                                                                alt="">

                                                            <span><a target="popup" data-bs-toggle="modal"
                                                                    data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                    {{ isset($socialLink->name) ? $socialLink->name : '' }}</a></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="data-set-foll d-flex align-items-center ">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/data-users.png"
                                                                alt="">
                                                            {{ @$socialLink->followers }} Follower
                                                        </div>
                                                    </td>
                                                @endif
                                                <td>
                                                    <div class="data-set-price d-flex align-items-center ">
                                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                                            alt="">
                                                        <?php
                                                        $newLivetreamPrice = 0;
                                                        $addNewLivetreamPrice = 0;

                                                        $user = App\Models\User::find($influencerRequestDetail->influencerdetails->user->id);

                                                        $fieldName = $influencerRequestDetail->advertising . '_price';
                                                        if ($user->advertisingMethodPrice != null) {
                                                            $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                        }
                                                        ?>
                                                        {{ number_format($influencerRequestDetail->current_price, 2) }}€
                                                    </div>

                                                </td>
                                                <td class="text-center">

                                                    @if ($row->finish == '1')
                                                        Finished
                                                    @elseif(@$row->status == 'Cancelled')
                                                        Cancelled
                                                    @elseif(@$row->status == '2')
                                                        Payment Submitted
                                                    @else
                                                        <input type="hidden" name="ids[]"
                                                            value="{{ @$row->id }}">
                                                        <select name="finish[]">
                                                            <option value="">Select</option>
                                                            <option {{ @$row->status == 'Cancelled' ? 'selected' : '' }}>
                                                                Cancelled</option>
                                                        </select>
                                                    @endif

                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach
                                    @if ($actionCount == 0)
                                        <tr>
                                            <td class="text-center">Empty</td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                            <div class="tab-pane fade" id="inprogress{{ $row->id }}" role="tabpanel"
                                aria-labelledby="inprogress-tab{{ $row->id }}">
                                <table>
                                    @php $inprogressCount = 0; @endphp
                                    @foreach ($influencerRequestDetails as $influencerRequestDetail)
                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                        @if (empty($influencerRequestDetail->influencerdetails))
                                            @php continue; @endphp
                                        @endif
                                        @php
                                            $time =
                                                isset($influencerRequestDetail->influencer_request_accepts->request_time_accept) &&
                                                $influencerRequestDetail->influencer_request_accepts->request_time_accept == 1
                                                    ? $influencerRequestDetail->influencer_request_accepts->request_time + $influencerRequestDetail->time
                                                    : $influencerRequestDetail->time;

                                            $created_date = date('Y-m-d H:i:s', strtotime($influencerRequestDetail->created_at));
                                            $updated_date = date('Y-m-d H:i:s', strtotime($influencerRequestDetail->updated_at));
                                            $campaignDate = date(
                                                'Y-m-d H:i:s',
                                                strtotime($updated_date . ' + ' . $time . ' days'),
                                            );
                                            $date = date('Y-m-d H:i:s');
                                            $seconds = strtotime($campaignDate) - strtotime($date);
                                            $days = floor($seconds / 86400);

                                        @endphp
                                        @if (
                                            ($days >= 0 && ($influencerRequestDetail->refund_reason != 'Cancelled' && !isset($influencerRequestDetail->social_posts) && $influencerRequestDetail->review != '1')) ||
                                                ($influencerRequestDetail->is_complained == 1 && $influencerRequestDetail->refund_reason != 'Complaint Confirmed' && $influencerRequestDetail->review != '1'))
                                            @php $inprogressCount ++; @endphp

                                            <tr class="accordian-table inprogress">
                                                <td>
                                                    <img src="{{ asset('/') }}/assets/front-end/images/data-time.svg"
                                                        alt="">
                                                </td>

                                                @php
                                                    $socialLink = App\Models\SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user->id)
                                                        ->where('media', $row->media)
                                                        ->first();
                                                @endphp

                                                @if ($socialLink != '')
                                                    <td>
                                                        <div class="data-set-user d-flex align-items-center ">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/data-user.png"
                                                                alt="">
                                                            <span><a target="popup" data-bs-toggle="modal"
                                                                    data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                    {{ isset($socialLink->name) ? $socialLink->name : '' }}</a></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="data-set-foll d-flex align-items-center ">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/data-users.png"
                                                                alt="">
                                                            {{ @$socialLink->followers }} Follower
                                                        </div>
                                                    </td>
                                                @endif
                                                <td>
                                                    <div class="data-set-price d-flex align-items-center ">
                                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                                            alt="">
                                                        <?php
                                                        $newLivetreamPrice = 0;
                                                        $addNewLivetreamPrice = 0;

                                                        $user = App\Models\User::find($influencerRequestDetail->influencerdetails->user->id);

                                                        $fieldName = $influencerRequestDetail->advertising . '_price';
                                                        if ($user->advertisingMethodPrice != null) {
                                                            $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                        }
                                                        ?>
                                                        {{ number_format($influencerRequestDetail->current_price, 2) }} €
                                                    </div>

                                                </td>
                                                <td class="text-center">
                                                    @if ($row->finish == '1')
                                                        Finished
                                                    @elseif(@$row->status == 'Cancelled')
                                                        Cancelled
                                                    @elseif(@$row->status == '2')
                                                        Payment Submitted
                                                    @else
                                                        <input type="hidden" name="ids[]"
                                                            value="{{ @$row->id }}">
                                                        <select name="finish[]">
                                                            <option value="">Select</option>
                                                            <option {{ @$row->status == 'Cancelled' ? 'selected' : '' }}>
                                                                Cancelled</option>
                                                        </select>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach
                                    @if ($inprogressCount == 0)
                                        <tr>
                                            <td class="text-center">Empty</td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                            <div class="tab-pane fade" id="completed{{ $row->id }}" role="tabpanel"
                                aria-labelledby="completed-tab{{ $row->id }}">
                                <table>
                                    @php $completedCount = 0; @endphp
                                    @foreach ($influencerRequestDetails as $influencerRequestDetail)
                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                        @if (empty($influencerRequestDetail->influencerdetails))
                                            @php continue; @endphp
                                        @endif
                                        @if (
                                            $influencerRequestDetail->finish == 1 ||
                                                $influencerRequestDetail->review == 1 ||
                                                (isset($influencerRequestDetail->influencer_request_accepts->complaints) &&
                                                    $influencerRequestDetail->influencer_request_accepts->complaints->status == 'Cancelled'))
                                            @php $completedCount++; @endphp

                                            <tr class="accordian-table completed">
                                                <td>
                                                    <img src="{{ asset('/') }}/assets/front-end/images/data-check.png"
                                                        alt="">
                                                </td>
                                                @php
                                                    $socialLink = App\Models\SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user->id)
                                                        ->where('media', $row->media)
                                                        ->first();
                                                @endphp
                                                @if (isset($socialLink))
                                                    <td>
                                                        <div class="data-set-user d-flex align-items-center ">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/data-user.png"
                                                                alt="">
                                                            <span><a target="popup" data-bs-toggle="modal"
                                                                    data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                    {{ isset($socialLink->name) ? $socialLink->name : '' }}</a></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="data-set-foll d-flex align-items-center ">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/data-users.png"
                                                                alt="">
                                                            {{ @$socialLink->followers }} Follower
                                                        </div>
                                                    </td>
                                                @endif
                                                <td>
                                                    <div class="data-set-price d-flex align-items-center ">
                                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                                            alt="">
                                                        <?php
                                                        $newLivetreamPrice = 0;
                                                        $addNewLivetreamPrice = 0;

                                                        $user = App\Models\User::find($influencerRequestDetail->influencerdetails->user->id);

                                                        $fieldName = $influencerRequestDetail->advertising . '_price';
                                                        if ($user->advertisingMethodPrice != null) {
                                                            $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                        }
                                                        ?>
                                                        {{ number_format($influencerRequestDetail->current_price, 2) }} €
                                                    </div>

                                                </td>
                                                <td class="text-center">
                                                    @if ($row->finish == '1')
                                                        Finished
                                                    @elseif(@$row->status == 'Cancelled')
                                                        Cancelled
                                                    @elseif(@$row->status == '2')
                                                        Payment Submitted
                                                    @else
                                                        <input type="hidden" name="ids[]"
                                                            value="{{ @$row->id }}">
                                                        <select name="finish[]">
                                                            <option value="">Select</option>
                                                            <option {{ @$row->status == 'Cancelled' ? 'selected' : '' }}>
                                                                Cancelled</option>
                                                        </select>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach

                                    @if ($completedCount == 0)
                                        <tr>
                                            <td class="text-center">Empty</td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                            <div class="tab-pane fade" id="cancelled{{ $row->id }}" role="tabpanel"
                                aria-labelledby="cancelled-tab{{ $row->id }}">
                                <table>
                                    @php $cancelledCount = 0; @endphp

                                    @foreach ($influencerRequestDetails as $influencerRequestDetail)
                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                        @if (empty($influencerRequestDetail->influencerdetails))
                                            @php continue; @endphp
                                        @endif
                                        @if (
                                            $influencerRequestDetail->refund_reason == 'Cancelled' ||
                                                $influencerRequestDetail->refund_reason == 'Complaint Confirmed' ||
                                                (isset($influencerRequestDetail->influencer_request_accepts->complaints) &&
                                                    $influencerRequestDetail->influencer_request_accepts->complaints->status == 'Confirmed'))
                                            @php $cancelledCount++; @endphp
                                            <tr class="accordian-table cancelled">
                                                <td>
                                                    <img src="{{ asset('/') }}/assets/front-end/images/data-close.png"
                                                        alt="">
                                                </td>
                                                @php
                                                    $socialLink = App\Models\SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user->id)
                                                        ->where('media', $row->media)
                                                        ->first();
                                                @endphp
                                                @if (isset($socialLink))
                                                    <td>
                                                        <div class="data-set-user d-flex align-items-center ">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/data-user.png"
                                                                alt="">

                                                            <span><a target="popup" data-bs-toggle="modal"
                                                                    data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                    {{ isset($socialLink->name) ? $socialLink->name : '' }}</a></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="data-set-foll d-flex align-items-center ">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/data-users.png"
                                                                alt="">
                                                            {{ @$socialLink->followers }} Follower
                                                        </div>
                                                    </td>
                                                @endif
                                                <td>
                                                    <div class="data-set-price d-flex align-items-center ">
                                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                                            alt="">
                                                        <?php
                                                        $newLivetreamPrice = 0;
                                                        $addNewLivetreamPrice = 0;

                                                        $user = App\Models\User::find($influencerRequestDetail->influencerdetails->user->id);

                                                        $fieldName = $influencerRequestDetail->advertising . '_price';
                                                        if ($user->advertisingMethodPrice != null) {
                                                            $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                        }
                                                        ?>
                                                        {{ number_format($influencerRequestDetail->current_price, 2) }} €
                                                    </div>

                                                </td>
                                                <td class="text-center">

                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach

                                    @if ($cancelledCount == 0)
                                        <tr>
                                            <td class="text-center">Empty</td>
                                        </tr>
                                    @endif
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="modal fade influncer wewPopup request-popup" id="requestForm{{ $row->id }}"
                data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestFormLabel"
                aria-hidden="true">
                <div class="modal-dialog default-width modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-body">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt="">
                            </button>
                            <div class="popup-title">{{ $row->compaign_title }}</div>
                            <div class="popup-title-id">Campaign ID: {{ $row->compaign_id }}</div>
                            <form method="post" id="requestFormSubmit{{ $row->id }}"
                                action="{{ url('/request-form') }}" data-parsley-validate>
                                @csrf
                                <input type="hidden" name="influencer_request_detail_id"
                                    id="influencer_request_detail_id" value="{{ isset($row->id) ? $row->id : '' }}">
                                <ul class="nav nav-tabs ordertab" id="myTab{{ $row->id }}" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active"
                                            id="general-information-tab{{ $row->id }}" data-bs-toggle="tab"
                                            data-bs-target="#general-information{{ $row->id }}" type="button"
                                            role="tab" aria-controls="general-information{{ $row->id }}"
                                            aria-selected="true">General Information

                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="order-detail-tab{{ $row->id }}"
                                            data-bs-toggle="tab" data-bs-target="#order-detail{{ $row->id }}"
                                            type="button" role="tab"
                                            aria-controls="order-detail{{ $row->id }}"
                                            aria-selected="false">Influencer Tasks

                                        </button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="myTabContent">
                                    <div class="tab-pane fade show active"
                                        id="general-information{{ $row->id }}" role="tabpanel"
                                        aria-labelledby="general-information-tab{{ $row->id }}">

                                        <div class="inside-table request-content">
                                            @if (isset($row->user) && $row->user->count() > 0)
                                                <div class="inside-table-row">
                                                    <span class="type-label"> Company Name</span>
                                                    <span class="type-image"><img
                                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-user-black.svg"
                                                            class="" alt=""></span>
                                                    <span class="type-content">{{ $row->user->first_name }}
                                                        {{ $row->user->last_name }}</span>
                                                </div>
                                            @endif
                                            <div class="inside-table-row">
                                                <span class="type-label">Request date</span>
                                                <span class="type-image"><img
                                                        src="{{ asset('/') }}/assets/front-end/images/icons/icon-calender-black.svg"
                                                        class="" alt=""></span>
                                                <span
                                                    class="type-content">{{ date('d.m.Y', strtotime($row->created_at)) }}</span>
                                            </div>
                                            <div class="inside-table-row">
                                                <span class="type-label">Total cost</span>
                                                <span class="type-image"><img
                                                        src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                                        class="" alt=""></span>
                                                <span class="type-content">{{ number_format($row->total_amount, 2) }}
                                                    €</span>
                                            </div>
                                            <div class="inside-table-row">
                                                <span class="type-label">Social Media</span>
                                                <span class="type-image"><img
                                                        src="{{ asset('/') }}/assets/front-end/images/icons/icon-cb-{{ $row->media }}.svg"
                                                        class="" alt=""></span>
                                                <span class="text-capitalize">{{ ucfirst($row->media) }}</span>
                                            </div>
                                            <div class="inside-table-row">
                                                <span class="type-label">Brand name</span>
                                                <span class="type-image"><img
                                                        src="{{ asset('/') }}/assets/front-end/images/icons/icon-brandname-black.svg"
                                                        class="" alt=""></span>
                                                <span class="type-content">{{ $row->name }}</span>
                                            </div>
                                            <div class="inside-table-row">
                                                <span class="type-label">Campaign type</span>
                                                <span class="type-image"><img
                                                        src="{{ asset('/') }}/assets/front-end/images/icons/icon-boostme-black.svg"
                                                        class="" alt=""></span>
                                                <span>{{ $row->post_type }}</span>
                                            </div>
                                            @if (isset($row->category))
                                                <div class="inside-table-row">
                                                    <span class="type-label">Category</span>
                                                    <span class="type-image"><img
                                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-category-black.svg"
                                                            class="" alt=""></span>
                                                    <span class="type-content">{{ $row->category->name }}</span>
                                                </div>
                                            @endif
                                        </div>

                                    </div>
                                    <div class="tab-pane fade" id="order-detail{{ $row->id }}" role="tabpanel"
                                        aria-labelledby="order-detail-tab{{ $row->id }}">
                                        <div class="request-content-data icon-before">
                                            @php $tasks = $row->tasks ;   @endphp
                                            @if (isset($tasks) && $tasks != '')
                                                @foreach ($tasks as $task)
                                                    @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                                                        <div class="inside-table-row">
                                                            <div class="order-titles">
                                                                {{ $task->taskDetail->task }}
                                                            </div>
                                                            <div class="order-content">
                                                                {{ $task->value }}
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach


                                                @foreach ($tasks as $task)
                                                    @if (isset($task->taskDetail) && $task->type == 'Link')
                                                        <div class="inside-table-row">
                                                            <div class="order-titles">
                                                                {{ $task->taskDetail->task }}
                                                            </div>
                                                            <div class="order-content">
                                                                <div class="order-link">
                                                                    <div class="link"
                                                                        id="myInput{{ $task->id }}">
                                                                        {{ $task->value }}</div>
                                                                    <div class="copy-link">
                                                                        <a class="copy_text" id="jjhu"
                                                                            data-toggle="tooltip"
                                                                            title="Copy to Clipboard"
                                                                            href="{{ $task->value }}">
                                                                            <span class="">COPY</span>
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach


                                                @foreach ($tasks as $task)
                                                    @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                                                        <div class="inside-table-row">
                                                            <div class="order-titles">
                                                                {{ $task->taskDetail->task }}
                                                            </div>
                                                            <div class="order-content">

                                                                @if ($row->post_content_type == 'video')
                                                                    <video controls>
                                                                        <source
                                                                            src="{{ asset('storage/' . $task->value) }}"
                                                                            type="video/mp4">
                                                                    </video>
                                                                @else
                                                                    <a href="{{ asset('storage/' . $task->value }}"
                                                                        download>
                                                                        <img src="{{ asset('storage/' . $task->value }}"
                                                                            width="40">
                                                                    </a>
                                                                @endif
                                                                <!-- <a href="{{ asset('storage/' . $task->value }}" download>
                                                    <img src="{{ asset('storage/' . $task->value }}" width="40">
                                                 </a> -->
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach

                                                @foreach ($tasks as $task)
                                                    @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                                                        <div class="inside-table-row">
                                                            <div class="order-titles">
                                                                {{ $task->taskDetail->task }}
                                                            </div>
                                                            <div class="order-content">
                                                                @php $tags = explode(',', $task->value); @endphp
                                                                @foreach ($tags as $tag)
                                                                    @if ($tag)
                                                                        <div class="order-hash-tag">
                                                                            <img src="{{ asset('/assets/front-end/images/icon-hash.png') }}"
                                                                                alt="">
                                                                            {{ $tag }}
                                                                        </div>
                                                                    @endif
                                                                @endforeach

                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach

                                                @foreach ($tasks as $task)
                                                    @if (isset($task->taskDetail) && $task->type == 'Info')
                                                        <div class="inside-table-row">
                                                            <div class="order-titles">
                                                                {{ $task->taskDetail->task }}
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            @endif
                                        </div>

                                    </div>
                                </div>
                                <!-- Selected users //-->
                                <div class="popup2btns d-flex">

                                </div>

                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @php $influencerRequestDetails = App\Models\InfluencerRequestDetail::where('compaign_id',$row->compaign_id)->get(); @endphp
        @foreach ($influencerRequestDetails as $influencerRequestDetail)
            {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
            @if (empty($influencerRequestDetail->influencerdetails))
                @php continue; @endphp
            @endif
            @php
                $influencerSocialLinkDetail = App\Models\SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user->id)
                    ->where('media', $row->media)
                    ->first();
            @endphp
            @include('front-user.pages.market-step-detail-influencer')
        @endforeach
    @endforeach
@endif
