@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Disputes
@endsection

@section('content')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Disputes</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    
    <!--Begin Content-->
    <section class="content">

        <!-- Default box -->
        <div class="card">
            <div class="card-header">

                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip"
                            title="Collapse">
                        <i class="fas fa-minus"></i></button>
                    <!-- <button type="button" class="btn btn-tool" data-card-widget="remove" data-toggle="tooltip"
                            title="Remove">
                        <i class="fas fa-times"></i></button> -->
                </div>
            </div>
            <div class="card-body">
                <form action="" method="post" id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
                @csrf 
                
                    <!-- <input type="submit" class="btn btn-danger" value="Update"> -->
                    <table class="table" id="example1">
                        <thead>
                        <tr>
                            <th class="column-title">Created At </th>
                            <!-- <th class="column-title">Campaign ID</th> -->
                            <th class="column-title">Comment By</th>
                            <th class="column-title">Comment</th>  
                            <th class="column-title">File</th>  
                        </tr>
                        </thead>
                        <tbody>
                        <?php $i = 1;  ?>
                        @foreach($result as $row)
                            <tr>

                                <td>{{ @$row->created_at }}</td>
                                <!-- <td>{{ @$row->influencerRequestDetail->compaign_id }}</td> -->
                                <td> {{ @$row->user->email }}  </td> 
                                <td>{{ @$row->comment }}</td> 
                                <td><a href="{{asset('storage/' . @$row->file}}" download>
                                    <img src="{{ asset('storage/' . @$row->file }}" width="30">
                                 </a>
                                </td> 
                            </tr>
                            <?php $i++; ?>
                        @endforeach
                        
                        </tbody>
                    </table>
                </form>
            </div>
            <!-- /.card-body -->
            <div class="card-footer">
                {{--Footer--}}
            </div>
            <!-- /.card-footer-->
        </div>
        <!-- /.card -->

    </section>
    <!-- /.content -->
@endsection
@section('admin_script_codes')
 
@endsection
    
