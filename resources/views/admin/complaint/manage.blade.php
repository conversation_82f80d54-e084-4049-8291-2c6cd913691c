@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Complaints
@endsection

@section('content')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Complaints</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    
    <!--Begin Content-->
    <section class="content">

        <!-- Default box -->
        <div class="card">
            <div class="card-header">

                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip"
                            title="Collapse">
                        <i class="fas fa-minus"></i></button>
                    <!-- <button type="button" class="btn btn-tool" data-card-widget="remove" data-toggle="tooltip"
                            title="Remove">
                        <i class="fas fa-times"></i></button> -->
                </div>
            </div>
            <div class="card-body">
                <form action="" method="post" id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
                @csrf 
                
                    <input type="submit" class="btn btn-danger" value="Update">
                    <table class="table" id="example1">
                        <thead>
                        <tr>
                            <th class="column-title">Created At </th>
                            <th class="column-title">Campaign ID</th>
                            <th class="column-title">Comment</th>  
                            <th class="column-title">File</th>  
                            <th class="column-title">Complaint To</th>
                            <th class="column-title">Complaint By</th>
                            <th class="column-title">Status </th> 
                        </tr>
                        </thead>
                        <tbody>
                        <?php $i = 1;  ?>
                        @foreach($result as $row)
                            <tr>

                                <td>{{ @$row->created_at }}</td>
                                <td>{{ @$row->influencer_request_accepts->influencer_request_details->compaign_id }}</td>
                                <td>{{ @$row->comment }}</td> 
                                <td>
                                    @if(@$row->file != NULL)
                                    <a href="{{asset('storage/' . @$row->file}}" download>
                                        <img src="{{ asset('storage/' . @$row->file }}" width="90">
                                     </a>
                                     @else
                                     -
                                     @endif
                                </td> 
                                <td> {{ @$row->influencer_request_accepts->influencer_request_details->influencerdetails->user->email }}  </td>
                                <td> {{ @$row->user->email }} </td>
                                <td > 
                                    <input type="hidden" name="ids[]" value="{{ @$row->id }}">
                                    <select name="status[]" class="update_status" data-task="{{ $row->id }}">
                                        <option {{ @$row->status == 'Inprogress'?'selected':''}} value="Inprogress">In Progress</option>
                                        <option {{ @$row->status == 'Confirmed'?'selected':''}} value="Confirmed">Accept complaint</option>
                                        <option {{ @$row->status == 'Cancelled'?'selected':''}} value="Cancelled">Reject complaint</option>
                                    </select>
                                </td>

                            </tr>
                            <?php $i++; ?>
                        @endforeach
                        
                        </tbody>
                    </table>
                </form>
            </div>
            <!-- /.card-body -->
            <div class="card-footer">
                {{--Footer--}}
            </div>
            <!-- /.card-footer-->
        </div>
        <!-- /.card -->

    </section>
    <!-- /.content -->
@endsection
@section('admin_script_codes')
 <script type="text/javascript">
 $(document).on('change','.update_status',function(){
    var status     = $(this).val();
    var rowId      = $(this).attr('data-task');
    $.ajax({
    url: "{{URL::to('/admin/update-complaint-status')}}/"+rowId+"/"+status,  
    method: 'GET',  
    }).done(function (data) {    
    toastr.success(data.msg);
    setTimeout(function () {
        location.reload();
    }, 1000)
   }); 
 })
 </script>
@endsection
    
